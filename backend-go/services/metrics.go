package services

import (
	"runtime"
	"sync"
	"time"

	"adc-multi-languages/utils"
)

// MetricsService handles application metrics and monitoring
type MetricsService struct {
	mutex           sync.RWMutex
	requestCount    map[string]int64
	responseTime    map[string][]time.Duration
	errorCount      map[string]int64
	activeUsers     map[string]time.Time
	apiKeyUsage     map[string]int64
	translationUsage map[string]int64
	startTime       time.Time
}

// NewMetricsService creates a new metrics service
func NewMetricsService() *MetricsService {
	return &MetricsService{
		requestCount:     make(map[string]int64),
		responseTime:     make(map[string][]time.Duration),
		errorCount:       make(map[string]int64),
		activeUsers:      make(map[string]time.Time),
		apiKeyUsage:      make(map[string]int64),
		translationUsage: make(map[string]int64),
		startTime:        time.Now(),
	}
}

// RequestMetrics represents metrics for a single request
type RequestMetrics struct {
	Endpoint     string        `json:"endpoint"`
	Method       string        `json:"method"`
	StatusCode   int           `json:"status_code"`
	ResponseTime time.Duration `json:"response_time"`
	UserID       string        `json:"user_id,omitempty"`
	APIKeyID     string        `json:"api_key_id,omitempty"`
	Timestamp    time.Time     `json:"timestamp"`
}

// SystemMetrics represents system-level metrics
type SystemMetrics struct {
	Uptime           time.Duration `json:"uptime"`
	MemoryUsage      MemoryStats   `json:"memory_usage"`
	GoroutineCount   int           `json:"goroutine_count"`
	CPUCount         int           `json:"cpu_count"`
	RequestCount     int64         `json:"total_requests"`
	ErrorCount       int64         `json:"total_errors"`
	ActiveUsers      int           `json:"active_users"`
	AverageResponse  float64       `json:"average_response_time_ms"`
}

// MemoryStats represents memory usage statistics
type MemoryStats struct {
	Alloc        uint64 `json:"alloc_bytes"`
	TotalAlloc   uint64 `json:"total_alloc_bytes"`
	Sys          uint64 `json:"sys_bytes"`
	NumGC        uint32 `json:"num_gc"`
	HeapAlloc    uint64 `json:"heap_alloc_bytes"`
	HeapSys      uint64 `json:"heap_sys_bytes"`
	HeapInuse    uint64 `json:"heap_inuse_bytes"`
	StackInuse   uint64 `json:"stack_inuse_bytes"`
}

// EndpointMetrics represents metrics for a specific endpoint
type EndpointMetrics struct {
	Endpoint        string        `json:"endpoint"`
	RequestCount    int64         `json:"request_count"`
	ErrorCount      int64         `json:"error_count"`
	AverageResponse time.Duration `json:"average_response_time"`
	MinResponse     time.Duration `json:"min_response_time"`
	MaxResponse     time.Duration `json:"max_response_time"`
	ErrorRate       float64       `json:"error_rate"`
}

// RecordRequest records metrics for a request
func (ms *MetricsService) RecordRequest(metrics RequestMetrics) {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	endpoint := metrics.Method + " " + metrics.Endpoint

	// Increment request count
	ms.requestCount[endpoint]++

	// Record response time
	if ms.responseTime[endpoint] == nil {
		ms.responseTime[endpoint] = make([]time.Duration, 0)
	}
	ms.responseTime[endpoint] = append(ms.responseTime[endpoint], metrics.ResponseTime)

	// Keep only last 1000 response times to prevent memory growth
	if len(ms.responseTime[endpoint]) > 1000 {
		ms.responseTime[endpoint] = ms.responseTime[endpoint][len(ms.responseTime[endpoint])-1000:]
	}

	// Record errors
	if metrics.StatusCode >= 400 {
		ms.errorCount[endpoint]++
	}

	// Record active users
	if metrics.UserID != "" {
		ms.activeUsers[metrics.UserID] = time.Now()
	}

	// Record API key usage
	if metrics.APIKeyID != "" {
		ms.apiKeyUsage[metrics.APIKeyID]++
	}

	// Clean up old active users (older than 30 minutes)
	cutoff := time.Now().Add(-30 * time.Minute)
	for userID, lastSeen := range ms.activeUsers {
		if lastSeen.Before(cutoff) {
			delete(ms.activeUsers, userID)
		}
	}
}

// RecordTranslation records translation usage metrics
func (ms *MetricsService) RecordTranslation(provider string, creditsUsed int) {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	ms.translationUsage[provider] += int64(creditsUsed)
}

// GetSystemMetrics returns current system metrics
func (ms *MetricsService) GetSystemMetrics() SystemMetrics {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// Calculate total requests and errors
	var totalRequests, totalErrors int64
	var totalResponseTime time.Duration
	var responseCount int64

	for endpoint, count := range ms.requestCount {
		totalRequests += count
		totalErrors += ms.errorCount[endpoint]

		if times := ms.responseTime[endpoint]; len(times) > 0 {
			for _, duration := range times {
				totalResponseTime += duration
				responseCount++
			}
		}
	}

	var averageResponse float64
	if responseCount > 0 {
		averageResponse = float64(totalResponseTime.Nanoseconds()) / float64(responseCount) / 1e6 // Convert to milliseconds
	}

	return SystemMetrics{
		Uptime:          time.Since(ms.startTime),
		MemoryUsage:     ms.getMemoryStats(m),
		GoroutineCount:  runtime.NumGoroutine(),
		CPUCount:        runtime.NumCPU(),
		RequestCount:    totalRequests,
		ErrorCount:      totalErrors,
		ActiveUsers:     len(ms.activeUsers),
		AverageResponse: averageResponse,
	}
}

// getMemoryStats converts runtime.MemStats to our MemoryStats struct
func (ms *MetricsService) getMemoryStats(m runtime.MemStats) MemoryStats {
	return MemoryStats{
		Alloc:      m.Alloc,
		TotalAlloc: m.TotalAlloc,
		Sys:        m.Sys,
		NumGC:      m.NumGC,
		HeapAlloc:  m.HeapAlloc,
		HeapSys:    m.HeapSys,
		HeapInuse:  m.HeapInuse,
		StackInuse: m.StackInuse,
	}
}

// GetEndpointMetrics returns metrics for all endpoints
func (ms *MetricsService) GetEndpointMetrics() []EndpointMetrics {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	var metrics []EndpointMetrics

	for endpoint, requestCount := range ms.requestCount {
		errorCount := ms.errorCount[endpoint]
		responseTimes := ms.responseTime[endpoint]

		var avgResponse, minResponse, maxResponse time.Duration
		if len(responseTimes) > 0 {
			var total time.Duration
			minResponse = responseTimes[0]
			maxResponse = responseTimes[0]

			for _, duration := range responseTimes {
				total += duration
				if duration < minResponse {
					minResponse = duration
				}
				if duration > maxResponse {
					maxResponse = duration
				}
			}
			avgResponse = total / time.Duration(len(responseTimes))
		}

		errorRate := float64(0)
		if requestCount > 0 {
			errorRate = float64(errorCount) / float64(requestCount) * 100
		}

		metrics = append(metrics, EndpointMetrics{
			Endpoint:        endpoint,
			RequestCount:    requestCount,
			ErrorCount:      errorCount,
			AverageResponse: avgResponse,
			MinResponse:     minResponse,
			MaxResponse:     maxResponse,
			ErrorRate:       errorRate,
		})
	}

	return metrics
}

// GetAPIKeyMetrics returns API key usage metrics
func (ms *MetricsService) GetAPIKeyMetrics() map[string]int64 {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	// Return a copy to prevent external modification
	result := make(map[string]int64)
	for key, value := range ms.apiKeyUsage {
		result[key] = value
	}
	return result
}

// GetTranslationMetrics returns translation usage metrics
func (ms *MetricsService) GetTranslationMetrics() map[string]int64 {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	// Return a copy to prevent external modification
	result := make(map[string]int64)
	for provider, usage := range ms.translationUsage {
		result[provider] = usage
	}
	return result
}

// GetActiveUsers returns list of active user IDs
func (ms *MetricsService) GetActiveUsers() []string {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	var users []string
	for userID := range ms.activeUsers {
		users = append(users, userID)
	}
	return users
}

// Reset clears all metrics (useful for testing)
func (ms *MetricsService) Reset() {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	ms.requestCount = make(map[string]int64)
	ms.responseTime = make(map[string][]time.Duration)
	ms.errorCount = make(map[string]int64)
	ms.activeUsers = make(map[string]time.Time)
	ms.apiKeyUsage = make(map[string]int64)
	ms.translationUsage = make(map[string]int64)
	ms.startTime = time.Now()
}

// GetHealthStatus returns a simple health status
func (ms *MetricsService) GetHealthStatus() map[string]interface{} {
	systemMetrics := ms.GetSystemMetrics()
	
	status := "healthy"
	if systemMetrics.ErrorCount > 0 && float64(systemMetrics.ErrorCount)/float64(systemMetrics.RequestCount) > 0.1 {
		status = "degraded"
	}
	if systemMetrics.AverageResponse > 5000 { // 5 seconds
		status = "slow"
	}

	return map[string]interface{}{
		"status":           status,
		"uptime":           systemMetrics.Uptime.String(),
		"requests":         systemMetrics.RequestCount,
		"errors":           systemMetrics.ErrorCount,
		"active_users":     systemMetrics.ActiveUsers,
		"memory_mb":        systemMetrics.MemoryUsage.Alloc / 1024 / 1024,
		"goroutines":       systemMetrics.GoroutineCount,
		"avg_response_ms":  systemMetrics.AverageResponse,
	}
}

// Global metrics service instance
var globalMetricsService *MetricsService

// GetMetricsService returns the global metrics service instance
func GetMetricsService() *MetricsService {
	if globalMetricsService == nil {
		globalMetricsService = NewMetricsService()
		utils.Logger.Info("Metrics service initialized")
	}
	return globalMetricsService
}
