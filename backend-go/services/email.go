package services

import (
	"bytes"
	"fmt"
	"html/template"
	"net/smtp"
	"os"

	"adc-multi-languages/utils"
)

// EmailService handles email sending functionality
type EmailService struct {
	SMTPHost     string
	SMTPPort     string
	SMTPUsername string
	SMTPPassword string
	FromEmail    string
	FromName     string
}

// NewEmailService creates a new email service instance
func NewEmailService() *EmailService {
	return &EmailService{
		SMTPHost:     getEnvOrDefault("SMTP_HOST", "smtp.gmail.com"),
		SMTPPort:     getEnvOrDefault("SMTP_PORT", "587"),
		SMTPUsername: getEnvOrDefault("SMTP_USERNAME", ""),
		SMTPPassword: getEnvOrDefault("SMTP_PASSWORD", ""),
		FromEmail:    getEnvOrDefault("FROM_EMAIL", "<EMAIL>"),
		FromName:     getEnvOrDefault("FROM_NAME", "ADC Multi-Languages"),
	}
}

// EmailTemplate represents an email template
type EmailTemplate struct {
	Subject string
	HTML    string
	Text    string
}

// EmailData represents data for email templates
type EmailData struct {
	UserName        string
	VerificationURL string
	ResetURL        string
	AppName         string
	SupportEmail    string
}

// getEnvOrDefault gets environment variable or returns default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// SendEmail sends an email using SMTP
func (e *EmailService) SendEmail(to, subject, htmlBody, textBody string) error {
	// Skip sending if SMTP is not configured
	if e.SMTPUsername == "" || e.SMTPPassword == "" {
		utils.Logger.Info("SMTP not configured, skipping email send to: " + to)
		return nil
	}

	// Create message
	message := fmt.Sprintf("From: %s <%s>\r\n", e.FromName, e.FromEmail)
	message += fmt.Sprintf("To: %s\r\n", to)
	message += fmt.Sprintf("Subject: %s\r\n", subject)
	message += "MIME-Version: 1.0\r\n"
	message += "Content-Type: multipart/alternative; boundary=\"boundary\"\r\n\r\n"

	// Text part
	message += "--boundary\r\n"
	message += "Content-Type: text/plain; charset=\"utf-8\"\r\n\r\n"
	message += textBody + "\r\n\r\n"

	// HTML part
	message += "--boundary\r\n"
	message += "Content-Type: text/html; charset=\"utf-8\"\r\n\r\n"
	message += htmlBody + "\r\n\r\n"
	message += "--boundary--\r\n"

	// SMTP authentication
	auth := smtp.PlainAuth("", e.SMTPUsername, e.SMTPPassword, e.SMTPHost)

	// Send email
	addr := e.SMTPHost + ":" + e.SMTPPort
	err := smtp.SendMail(addr, auth, e.FromEmail, []string{to}, []byte(message))
	if err != nil {
		utils.Logger.Error("Failed to send email: " + err.Error())
		return err
	}

	utils.Logger.Info("Email sent successfully to: " + to)
	return nil
}

// SendPasswordResetEmail sends a password reset email
func (e *EmailService) SendPasswordResetEmail(to, userName, resetToken string) error {
	baseURL := getEnvOrDefault("FRONTEND_URL", "http://localhost:3000")
	resetURL := fmt.Sprintf("%s/reset-password?token=%s", baseURL, resetToken)

	data := EmailData{
		UserName:     userName,
		ResetURL:     resetURL,
		AppName:      "ADC Multi-Languages",
		SupportEmail: getEnvOrDefault("SUPPORT_EMAIL", "<EMAIL>"),
	}

	template := e.getPasswordResetTemplate()
	
	htmlBody, err := e.renderTemplate(template.HTML, data)
	if err != nil {
		return err
	}

	textBody, err := e.renderTemplate(template.Text, data)
	if err != nil {
		return err
	}

	return e.SendEmail(to, template.Subject, htmlBody, textBody)
}

// SendEmailVerificationEmail sends an email verification email
func (e *EmailService) SendEmailVerificationEmail(to, userName, verificationToken string) error {
	baseURL := getEnvOrDefault("FRONTEND_URL", "http://localhost:3000")
	verificationURL := fmt.Sprintf("%s/verify-email?token=%s", baseURL, verificationToken)

	data := EmailData{
		UserName:        userName,
		VerificationURL: verificationURL,
		AppName:         "ADC Multi-Languages",
		SupportEmail:    getEnvOrDefault("SUPPORT_EMAIL", "<EMAIL>"),
	}

	template := e.getEmailVerificationTemplate()
	
	htmlBody, err := e.renderTemplate(template.HTML, data)
	if err != nil {
		return err
	}

	textBody, err := e.renderTemplate(template.Text, data)
	if err != nil {
		return err
	}

	return e.SendEmail(to, template.Subject, htmlBody, textBody)
}

// renderTemplate renders a template with data
func (e *EmailService) renderTemplate(templateStr string, data EmailData) (string, error) {
	tmpl, err := template.New("email").Parse(templateStr)
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, data)
	if err != nil {
		return "", err
	}

	return buf.String(), nil
}

// getPasswordResetTemplate returns the password reset email template
func (e *EmailService) getPasswordResetTemplate() EmailTemplate {
	return EmailTemplate{
		Subject: "Reset Your Password - ADC Multi-Languages",
		HTML: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Reset Your Password</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c3e50;">Reset Your Password</h2>
        <p>Hello {{.UserName}},</p>
        <p>We received a request to reset your password for your {{.AppName}} account.</p>
        <p>Click the button below to reset your password:</p>
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{.ResetURL}}" style="background-color: #3498db; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
        </div>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #3498db;">{{.ResetURL}}</p>
        <p><strong>This link will expire in 1 hour for security reasons.</strong></p>
        <p>If you didn't request this password reset, please ignore this email or contact our support team.</p>
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        <p style="font-size: 12px; color: #666;">
            Best regards,<br>
            The {{.AppName}} Team<br>
            <a href="mailto:{{.SupportEmail}}">{{.SupportEmail}}</a>
        </p>
    </div>
</body>
</html>`,
		Text: `Reset Your Password - {{.AppName}}

Hello {{.UserName}},

We received a request to reset your password for your {{.AppName}} account.

Please click the following link to reset your password:
{{.ResetURL}}

This link will expire in 1 hour for security reasons.

If you didn't request this password reset, please ignore this email or contact our support team at {{.SupportEmail}}.

Best regards,
The {{.AppName}} Team`,
	}
}

// getEmailVerificationTemplate returns the email verification template
func (e *EmailService) getEmailVerificationTemplate() EmailTemplate {
	return EmailTemplate{
		Subject: "Verify Your Email - ADC Multi-Languages",
		HTML: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Verify Your Email</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c3e50;">Welcome to {{.AppName}}!</h2>
        <p>Hello {{.UserName}},</p>
        <p>Thank you for signing up for {{.AppName}}. To complete your registration, please verify your email address.</p>
        <p>Click the button below to verify your email:</p>
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{.VerificationURL}}" style="background-color: #27ae60; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Verify Email</a>
        </div>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #27ae60;">{{.VerificationURL}}</p>
        <p><strong>This link will expire in 24 hours.</strong></p>
        <p>If you didn't create an account with us, please ignore this email.</p>
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        <p style="font-size: 12px; color: #666;">
            Best regards,<br>
            The {{.AppName}} Team<br>
            <a href="mailto:{{.SupportEmail}}">{{.SupportEmail}}</a>
        </p>
    </div>
</body>
</html>`,
		Text: `Welcome to {{.AppName}}!

Hello {{.UserName}},

Thank you for signing up for {{.AppName}}. To complete your registration, please verify your email address.

Please click the following link to verify your email:
{{.VerificationURL}}

This link will expire in 24 hours.

If you didn't create an account with us, please ignore this email.

Best regards,
The {{.AppName}} Team
{{.SupportEmail}}`,
	}
}
