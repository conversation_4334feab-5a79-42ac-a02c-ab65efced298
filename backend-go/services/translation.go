package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
)

// TranslationService handles AI-powered translation functionality
type TranslationService struct {
	openAIAPIKey    string
	googleAPIKey    string
	azureAPIKey     string
	azureRegion     string
	azureEndpoint   string
	defaultProvider string
	httpClient      *http.Client
}

// NewTranslationService creates a new translation service instance
func NewTranslationService() *TranslationService {
	return &TranslationService{
		openAIAPIKey:    os.Getenv("OPENAI_API_KEY"),
		googleAPIKey:    os.Getenv("GOOGLE_TRANSLATE_API_KEY"),
		azureAPIKey:     os.Getenv("AZURE_TRANSLATOR_API_KEY"),
		azureRegion:     getEnvOrDefault("AZURE_TRANSLATOR_REGION", "eastus"),
		azureEndpoint:   getEnvOrDefault("AZURE_TRANSLATOR_ENDPOINT", "https://api.cognitive.microsofttranslator.com"),
		defaultProvider: getEnvOrDefault("DEFAULT_TRANSLATION_PROVIDER", "openai"),
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// TranslationRequest represents a translation request
type TranslationRequest struct {
	Text         string `json:"text"`
	SourceLang   string `json:"source_lang"`
	TargetLang   string `json:"target_lang"`
	Context      string `json:"context,omitempty"`
	Provider     string `json:"provider,omitempty"`
	MaxLength    int    `json:"max_length,omitempty"`
	Tone         string `json:"tone,omitempty"` // formal, casual, technical
	PreserveHTML bool   `json:"preserve_html,omitempty"`
}

// TranslationResponse represents a translation response
type TranslationResponse struct {
	TranslatedText   string   `json:"translated_text"`
	SourceLanguage   string   `json:"source_language"`
	TargetLanguage   string   `json:"target_language"`
	Provider         string   `json:"provider"`
	Confidence       float64  `json:"confidence,omitempty"`
	AlternativeTexts []string `json:"alternative_texts,omitempty"`
	DetectedLanguage string   `json:"detected_language,omitempty"`
	WordCount        int      `json:"word_count"`
	CharacterCount   int      `json:"character_count"`
	ProcessingTime   int64    `json:"processing_time_ms"`
}

// TranslationError represents a translation error
type TranslationError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *TranslationError) Error() string {
	return fmt.Sprintf("Translation error [%s]: %s", e.Code, e.Message)
}

// Translate translates text using the specified or default provider
func (ts *TranslationService) Translate(req TranslationRequest) (*TranslationResponse, error) {
	startTime := time.Now()

	// Validate request
	if err := ts.validateRequest(req); err != nil {
		return nil, err
	}

	// Determine provider
	provider := req.Provider
	if provider == "" {
		provider = ts.defaultProvider
	}

	// Perform translation based on provider
	var response *TranslationResponse
	var err error

	switch strings.ToLower(provider) {
	case "openai":
		response, err = ts.translateWithOpenAI(req)
	case "google":
		response, err = ts.translateWithGoogle(req)
	case "azure":
		response, err = ts.translateWithAzure(req)
	default:
		return nil, &TranslationError{
			Code:    "INVALID_PROVIDER",
			Message: fmt.Sprintf("Unsupported translation provider: %s", provider),
		}
	}

	if err != nil {
		return nil, err
	}

	// Add metadata
	response.ProcessingTime = time.Since(startTime).Milliseconds()
	response.WordCount = len(strings.Fields(req.Text))
	response.CharacterCount = len(req.Text)
	response.Provider = provider

	return response, nil
}

// validateRequest validates the translation request
func (ts *TranslationService) validateRequest(req TranslationRequest) error {
	if req.Text == "" {
		return &TranslationError{
			Code:    "EMPTY_TEXT",
			Message: "Text to translate cannot be empty",
		}
	}

	if len(req.Text) > 10000 {
		return &TranslationError{
			Code:    "TEXT_TOO_LONG",
			Message: "Text exceeds maximum length of 10,000 characters",
		}
	}

	if req.SourceLang == "" {
		return &TranslationError{
			Code:    "MISSING_SOURCE_LANG",
			Message: "Source language is required",
		}
	}

	if req.TargetLang == "" {
		return &TranslationError{
			Code:    "MISSING_TARGET_LANG",
			Message: "Target language is required",
		}
	}

	if req.SourceLang == req.TargetLang {
		return &TranslationError{
			Code:    "SAME_LANGUAGES",
			Message: "Source and target languages cannot be the same",
		}
	}

	return nil
}

// translateWithOpenAI translates text using OpenAI GPT
func (ts *TranslationService) translateWithOpenAI(req TranslationRequest) (*TranslationResponse, error) {
	if ts.openAIAPIKey == "" {
		return nil, &TranslationError{
			Code:    "MISSING_API_KEY",
			Message: "OpenAI API key not configured",
		}
	}

	// Build prompt
	prompt := ts.buildOpenAIPrompt(req)

	// Prepare request
	requestBody := map[string]interface{}{
		"model": "gpt-3.5-turbo",
		"messages": []map[string]string{
			{
				"role":    "system",
				"content": "You are a professional translator. Provide only the translation without any additional text or explanations.",
			},
			{
				"role":    "user",
				"content": prompt,
			},
		},
		"max_tokens":        2000,
		"temperature":       0.3,
		"top_p":             1,
		"presence_penalty":  0,
		"frequency_penalty": 0,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, &TranslationError{
			Code:    "REQUEST_ENCODING_ERROR",
			Message: "Failed to encode request",
			Details: err.Error(),
		}
	}

	// Make API request
	httpReq, err := http.NewRequest("POST", "https://api.openai.com/v1/chat/completions", bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, &TranslationError{
			Code:    "REQUEST_CREATION_ERROR",
			Message: "Failed to create HTTP request",
			Details: err.Error(),
		}
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+ts.openAIAPIKey)

	resp, err := ts.httpClient.Do(httpReq)
	if err != nil {
		return nil, &TranslationError{
			Code:    "API_REQUEST_ERROR",
			Message: "Failed to make API request",
			Details: err.Error(),
		}
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, &TranslationError{
			Code:    "RESPONSE_READ_ERROR",
			Message: "Failed to read API response",
			Details: err.Error(),
		}
	}

	if resp.StatusCode != http.StatusOK {
		return nil, &TranslationError{
			Code:    "API_ERROR",
			Message: fmt.Sprintf("OpenAI API returned status %d", resp.StatusCode),
			Details: string(body),
		}
	}

	// Parse response
	var openAIResp struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
		Usage struct {
			TotalTokens int `json:"total_tokens"`
		} `json:"usage"`
	}

	if err := json.Unmarshal(body, &openAIResp); err != nil {
		return nil, &TranslationError{
			Code:    "RESPONSE_PARSE_ERROR",
			Message: "Failed to parse API response",
			Details: err.Error(),
		}
	}

	if len(openAIResp.Choices) == 0 {
		return nil, &TranslationError{
			Code:    "NO_TRANSLATION",
			Message: "No translation returned from OpenAI",
		}
	}

	translatedText := strings.TrimSpace(openAIResp.Choices[0].Message.Content)

	return &TranslationResponse{
		TranslatedText: translatedText,
		SourceLanguage: req.SourceLang,
		TargetLanguage: req.TargetLang,
		Confidence:     0.95, // OpenAI doesn't provide confidence scores
	}, nil
}

// buildOpenAIPrompt builds a prompt for OpenAI translation
func (ts *TranslationService) buildOpenAIPrompt(req TranslationRequest) string {
	prompt := fmt.Sprintf("Translate the following text from %s to %s:\n\n%s", req.SourceLang, req.TargetLang, req.Text)

	if req.Context != "" {
		prompt = fmt.Sprintf("Context: %s\n\n%s", req.Context, prompt)
	}

	if req.Tone != "" {
		prompt += fmt.Sprintf("\n\nPlease use a %s tone.", req.Tone)
	}

	if req.PreserveHTML {
		prompt += "\n\nPreserve any HTML tags in the translation."
	}

	return prompt
}

// translateWithGoogle translates text using Google Translate API
func (ts *TranslationService) translateWithGoogle(req TranslationRequest) (*TranslationResponse, error) {
	if ts.googleAPIKey == "" {
		return nil, &TranslationError{
			Code:    "MISSING_API_KEY",
			Message: "Google Translate API key not configured",
		}
	}

	// Build URL
	url := fmt.Sprintf("https://translation.googleapis.com/language/translate/v2?key=%s", ts.googleAPIKey)

	// Prepare request
	requestBody := map[string]interface{}{
		"q":      req.Text,
		"source": req.SourceLang,
		"target": req.TargetLang,
		"format": "text",
	}

	if req.PreserveHTML {
		requestBody["format"] = "html"
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, &TranslationError{
			Code:    "REQUEST_ENCODING_ERROR",
			Message: "Failed to encode request",
			Details: err.Error(),
		}
	}

	// Make API request
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, &TranslationError{
			Code:    "REQUEST_CREATION_ERROR",
			Message: "Failed to create HTTP request",
			Details: err.Error(),
		}
	}

	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := ts.httpClient.Do(httpReq)
	if err != nil {
		return nil, &TranslationError{
			Code:    "API_REQUEST_ERROR",
			Message: "Failed to make API request",
			Details: err.Error(),
		}
	}
	defer resp.Body.Close()

	// Read and parse response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, &TranslationError{
			Code:    "RESPONSE_READ_ERROR",
			Message: "Failed to read API response",
			Details: err.Error(),
		}
	}

	if resp.StatusCode != http.StatusOK {
		return nil, &TranslationError{
			Code:    "API_ERROR",
			Message: fmt.Sprintf("Google Translate API returned status %d", resp.StatusCode),
			Details: string(body),
		}
	}

	var googleResp struct {
		Data struct {
			Translations []struct {
				TranslatedText         string `json:"translatedText"`
				DetectedSourceLanguage string `json:"detectedSourceLanguage,omitempty"`
			} `json:"translations"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &googleResp); err != nil {
		return nil, &TranslationError{
			Code:    "RESPONSE_PARSE_ERROR",
			Message: "Failed to parse API response",
			Details: err.Error(),
		}
	}

	if len(googleResp.Data.Translations) == 0 {
		return nil, &TranslationError{
			Code:    "NO_TRANSLATION",
			Message: "No translation returned from Google",
		}
	}

	translation := googleResp.Data.Translations[0]

	return &TranslationResponse{
		TranslatedText:   translation.TranslatedText,
		SourceLanguage:   req.SourceLang,
		TargetLanguage:   req.TargetLang,
		DetectedLanguage: translation.DetectedSourceLanguage,
		Confidence:       0.90, // Google doesn't provide confidence scores in this API
	}, nil
}

// translateWithAzure translates text using Azure Translator
func (ts *TranslationService) translateWithAzure(req TranslationRequest) (*TranslationResponse, error) {
	if ts.azureAPIKey == "" {
		return nil, &TranslationError{
			Code:    "MISSING_API_KEY",
			Message: "Azure Translator API key not configured",
		}
	}

	// Build URL
	url := fmt.Sprintf("%s/translate?api-version=3.0&from=%s&to=%s", ts.azureEndpoint, req.SourceLang, req.TargetLang)

	// Prepare request
	requestBody := []map[string]string{
		{"text": req.Text},
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, &TranslationError{
			Code:    "REQUEST_ENCODING_ERROR",
			Message: "Failed to encode request",
			Details: err.Error(),
		}
	}

	// Make API request
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, &TranslationError{
			Code:    "REQUEST_CREATION_ERROR",
			Message: "Failed to create HTTP request",
			Details: err.Error(),
		}
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Ocp-Apim-Subscription-Key", ts.azureAPIKey)
	if ts.azureRegion != "" {
		httpReq.Header.Set("Ocp-Apim-Subscription-Region", ts.azureRegion)
	}

	resp, err := ts.httpClient.Do(httpReq)
	if err != nil {
		return nil, &TranslationError{
			Code:    "API_REQUEST_ERROR",
			Message: "Failed to make API request",
			Details: err.Error(),
		}
	}
	defer resp.Body.Close()

	// Read and parse response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, &TranslationError{
			Code:    "RESPONSE_READ_ERROR",
			Message: "Failed to read API response",
			Details: err.Error(),
		}
	}

	if resp.StatusCode != http.StatusOK {
		return nil, &TranslationError{
			Code:    "API_ERROR",
			Message: fmt.Sprintf("Azure Translator API returned status %d", resp.StatusCode),
			Details: string(body),
		}
	}

	var azureResp []struct {
		Translations []struct {
			Text string `json:"text"`
			To   string `json:"to"`
		} `json:"translations"`
		DetectedLanguage struct {
			Language string  `json:"language"`
			Score    float64 `json:"score"`
		} `json:"detectedLanguage,omitempty"`
	}

	if err := json.Unmarshal(body, &azureResp); err != nil {
		return nil, &TranslationError{
			Code:    "RESPONSE_PARSE_ERROR",
			Message: "Failed to parse API response",
			Details: err.Error(),
		}
	}

	if len(azureResp) == 0 || len(azureResp[0].Translations) == 0 {
		return nil, &TranslationError{
			Code:    "NO_TRANSLATION",
			Message: "No translation returned from Azure",
		}
	}

	translation := azureResp[0].Translations[0]
	detectedLang := azureResp[0].DetectedLanguage

	return &TranslationResponse{
		TranslatedText:   translation.Text,
		SourceLanguage:   req.SourceLang,
		TargetLanguage:   req.TargetLang,
		DetectedLanguage: detectedLang.Language,
		Confidence:       detectedLang.Score,
	}, nil
}

// GetSupportedLanguages returns supported languages for a provider
func (ts *TranslationService) GetSupportedLanguages(provider string) ([]string, error) {
	switch strings.ToLower(provider) {
	case "openai":
		return ts.getOpenAISupportedLanguages(), nil
	case "google":
		return ts.getGoogleSupportedLanguages()
	case "azure":
		return ts.getAzureSupportedLanguages()
	default:
		return nil, &TranslationError{
			Code:    "INVALID_PROVIDER",
			Message: fmt.Sprintf("Unsupported translation provider: %s", provider),
		}
	}
}

// getOpenAISupportedLanguages returns languages supported by OpenAI
func (ts *TranslationService) getOpenAISupportedLanguages() []string {
	return []string{
		"en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh",
		"ar", "hi", "tr", "pl", "nl", "sv", "da", "no", "fi", "cs",
		"hu", "ro", "bg", "hr", "sk", "sl", "et", "lv", "lt", "mt",
		"ga", "cy", "eu", "ca", "gl", "is", "mk", "sq", "sr", "bs",
		"me", "hr", "sl", "sk", "cs", "hu", "ro", "bg", "el", "he",
		"th", "vi", "id", "ms", "tl", "sw", "am", "bn", "gu", "kn",
		"ml", "mr", "ne", "or", "pa", "si", "ta", "te", "ur", "fa",
	}
}

// getGoogleSupportedLanguages returns languages supported by Google Translate
func (ts *TranslationService) getGoogleSupportedLanguages() ([]string, error) {
	// In a real implementation, you would call the Google Translate API
	// to get the current list of supported languages
	return []string{
		"af", "sq", "am", "ar", "hy", "az", "eu", "be", "bn", "bs",
		"bg", "ca", "ceb", "ny", "zh", "co", "hr", "cs", "da", "nl",
		"en", "eo", "et", "tl", "fi", "fr", "fy", "gl", "ka", "de",
		"el", "gu", "ht", "ha", "haw", "iw", "hi", "hmn", "hu", "is",
		"ig", "id", "ga", "it", "ja", "jw", "kn", "kk", "km", "ko",
		"ku", "ky", "lo", "la", "lv", "lt", "lb", "mk", "mg", "ms",
		"ml", "mt", "mi", "mr", "mn", "my", "ne", "no", "ps", "fa",
		"pl", "pt", "pa", "ro", "ru", "sm", "gd", "sr", "st", "sn",
		"sd", "si", "sk", "sl", "so", "es", "su", "sw", "sv", "tg",
		"ta", "te", "th", "tr", "uk", "ur", "uz", "vi", "cy", "xh",
		"yi", "yo", "zu",
	}, nil
}

// getAzureSupportedLanguages returns languages supported by Azure Translator
func (ts *TranslationService) getAzureSupportedLanguages() ([]string, error) {
	// In a real implementation, you would call the Azure Translator API
	// to get the current list of supported languages
	return []string{
		"af", "sq", "am", "ar", "hy", "as", "az", "bn", "ba", "eu",
		"be", "bs", "bg", "ca", "zh", "hr", "cs", "da", "prs", "dv",
		"nl", "en", "et", "fo", "fj", "fil", "fi", "fr", "gl", "ka",
		"de", "el", "gu", "ht", "he", "hi", "mww", "hu", "is", "id",
		"iu", "ga", "it", "ja", "kn", "kk", "km", "tlh", "ko", "ku",
		"ky", "lo", "lv", "lt", "mg", "ms", "ml", "mt", "mi", "mr",
		"mn", "my", "ne", "nb", "or", "ps", "fa", "pl", "pt", "pa",
		"otq", "ro", "ru", "sm", "sr", "sk", "sl", "so", "es", "sw",
		"sv", "ty", "ta", "tt", "te", "th", "bo", "ti", "to", "tr",
		"tk", "uk", "ur", "ug", "uz", "vi", "cy", "yua", "yue", "zu",
	}, nil
}

// DetectLanguage detects the language of the given text
func (ts *TranslationService) DetectLanguage(text string, provider string) (string, float64, error) {
	if provider == "" {
		provider = ts.defaultProvider
	}

	switch strings.ToLower(provider) {
	case "google":
		return ts.detectLanguageWithGoogle(text)
	case "azure":
		return ts.detectLanguageWithAzure(text)
	default:
		return "", 0, &TranslationError{
			Code:    "UNSUPPORTED_OPERATION",
			Message: fmt.Sprintf("Language detection not supported for provider: %s", provider),
		}
	}
}

// detectLanguageWithGoogle detects language using Google Translate API
func (ts *TranslationService) detectLanguageWithGoogle(text string) (string, float64, error) {
	if ts.googleAPIKey == "" {
		return "", 0, &TranslationError{
			Code:    "MISSING_API_KEY",
			Message: "Google Translate API key not configured",
		}
	}

	url := fmt.Sprintf("https://translation.googleapis.com/language/translate/v2/detect?key=%s", ts.googleAPIKey)

	requestBody := map[string]interface{}{
		"q": text,
	}

	jsonBody, _ := json.Marshal(requestBody)
	httpReq, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := ts.httpClient.Do(httpReq)
	if err != nil {
		return "", 0, err
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	var googleResp struct {
		Data struct {
			Detections [][]struct {
				Language   string  `json:"language"`
				Confidence float64 `json:"confidence"`
			} `json:"detections"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &googleResp); err != nil {
		return "", 0, err
	}

	if len(googleResp.Data.Detections) > 0 && len(googleResp.Data.Detections[0]) > 0 {
		detection := googleResp.Data.Detections[0][0]
		return detection.Language, detection.Confidence, nil
	}

	return "", 0, &TranslationError{
		Code:    "NO_DETECTION",
		Message: "Could not detect language",
	}
}

// detectLanguageWithAzure detects language using Azure Translator
func (ts *TranslationService) detectLanguageWithAzure(text string) (string, float64, error) {
	if ts.azureAPIKey == "" {
		return "", 0, &TranslationError{
			Code:    "MISSING_API_KEY",
			Message: "Azure Translator API key not configured",
		}
	}

	url := fmt.Sprintf("%s/detect?api-version=3.0", ts.azureEndpoint)

	requestBody := []map[string]string{
		{"text": text},
	}

	jsonBody, _ := json.Marshal(requestBody)
	httpReq, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Ocp-Apim-Subscription-Key", ts.azureAPIKey)
	if ts.azureRegion != "" {
		httpReq.Header.Set("Ocp-Apim-Subscription-Region", ts.azureRegion)
	}

	resp, err := ts.httpClient.Do(httpReq)
	if err != nil {
		return "", 0, err
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	var azureResp []struct {
		Language string  `json:"language"`
		Score    float64 `json:"score"`
	}

	if err := json.Unmarshal(body, &azureResp); err != nil {
		return "", 0, err
	}

	if len(azureResp) > 0 {
		return azureResp[0].Language, azureResp[0].Score, nil
	}

	return "", 0, &TranslationError{
		Code:    "NO_DETECTION",
		Message: "Could not detect language",
	}
}
