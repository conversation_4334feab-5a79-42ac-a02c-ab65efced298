package handlers

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// APIKeyUsageHandler handles API key usage analytics-related requests
type APIKeyUsageHandler struct{}

// NewAPIKeyUsageHandler creates a new API key usage handler
func NewAPIKeyUsageHandler() *APIKeyUsageHandler {
	return &APIKeyUsageHandler{}
}

// APIKeyUsageResponse represents the response structure for API key usage
type APIKeyUsageResponse struct {
	APIKeyID      string                    `json:"api_key_id"`
	APIKeyName    string                    `json:"api_key_name"`
	TotalCalls    int64                     `json:"total_calls"`
	SuccessfulCalls int64                   `json:"successful_calls"`
	ErrorCalls    int64                     `json:"error_calls"`
	SuccessRate   float64                   `json:"success_rate"`
	LastUsed      *string                   `json:"last_used"`
	DateRange     DateRange                 `json:"date_range"`
	ByEndpoint    []EndpointUsage           `json:"by_endpoint"`
	ByDay         []DailyUsage              `json:"by_day"`
	ByStatusCode  []StatusCodeUsage         `json:"by_status_code"`
}

// EndpointUsage represents usage statistics for a specific endpoint
type EndpointUsage struct {
	Endpoint      string  `json:"endpoint"`
	Method        string  `json:"method"`
	CallCount     int64   `json:"call_count"`
	SuccessRate   float64 `json:"success_rate"`
	AvgResponseTime float64 `json:"avg_response_time_ms"`
}

// DailyUsage represents usage statistics for a specific day
type DailyUsage struct {
	Date        string `json:"date"`
	CallCount   int64  `json:"call_count"`
	SuccessRate float64 `json:"success_rate"`
}

// StatusCodeUsage represents usage statistics by HTTP status code
type StatusCodeUsage struct {
	StatusCode int   `json:"status_code"`
	Count      int64 `json:"count"`
	Percentage float64 `json:"percentage"`
}

// DateRange represents a date range
type DateRange struct {
	From string `json:"from"`
	To   string `json:"to"`
}

// GetAPIKeyUsage handles GET /api/organizations/:orgId/api-keys/:id/usage
func (h *APIKeyUsageHandler) GetAPIKeyUsage(c *gin.Context) {
	orgID := c.Param("orgId")
	apiKeyID := c.Param("id")

	// Validate IDs
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	apiKeyUUID, err := uuid.Parse(apiKeyID)
	if err != nil {
		utils.BadRequest(c, "Invalid API key ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Parse date range parameters
	fromStr := c.DefaultQuery("from", "")
	toStr := c.DefaultQuery("to", "")
	
	var fromDate, toDate time.Time
	if fromStr != "" {
		fromDate, err = utils.ParseDateString(fromStr)
		if err != nil {
			utils.BadRequest(c, "Invalid 'from' date format. Use YYYY-MM-DD")
			return
		}
	} else {
		fromDate = utils.GetCurrentTime().AddDate(0, 0, -30) // Default to 30 days ago
	}

	if toStr != "" {
		toDate, err = utils.ParseDateString(toStr)
		if err != nil {
			utils.BadRequest(c, "Invalid 'to' date format. Use YYYY-MM-DD")
			return
		}
	} else {
		toDate = utils.GetCurrentTime() // Default to now
	}

	db := database.GetDB()

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
		orgUUID, userUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Check if API key exists and belongs to the organization
	var apiKey models.APIKey
	if err := db.First(&apiKey, "id = ? AND organization_id = ?", apiKeyUUID, orgUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "API key not found")
			return
		}
		utils.InternalError(c, "Failed to fetch API key")
		return
	}

	// Get usage statistics
	usage := h.getAPIKeyUsageStats(db, apiKeyUUID, fromDate, toDate)
	usage.APIKeyID = apiKey.ID.String()
	usage.APIKeyName = apiKey.Name
	usage.DateRange = DateRange{
		From: utils.FormatTimestamp(fromDate),
		To:   utils.FormatTimestamp(toDate),
	}

	utils.Success(c, usage)
}

// GetAPIKeyUsageByEndpoint handles GET /api/organizations/:orgId/api-keys/:id/usage/by-endpoint
func (h *APIKeyUsageHandler) GetAPIKeyUsageByEndpoint(c *gin.Context) {
	orgID := c.Param("orgId")
	apiKeyID := c.Param("id")

	// Validate IDs
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	apiKeyUUID, err := uuid.Parse(apiKeyID)
	if err != nil {
		utils.BadRequest(c, "Invalid API key ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	db := database.GetDB()

	// Check access (same as above)
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
		orgUUID, userUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Check if API key exists
	var apiKey models.APIKey
	if err := db.First(&apiKey, "id = ? AND organization_id = ?", apiKeyUUID, orgUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "API key not found")
			return
		}
		utils.InternalError(c, "Failed to fetch API key")
		return
	}

	// Get endpoint usage statistics
	endpointUsage := h.getEndpointUsageStats(db, apiKeyUUID, page, limit)

	utils.SuccessWithPagination(c, endpointUsage, uint32(page), uint32(limit), uint64(len(endpointUsage)))
}

// GetAPIKeyUsageByDay handles GET /api/organizations/:orgId/api-keys/:id/usage/by-day
func (h *APIKeyUsageHandler) GetAPIKeyUsageByDay(c *gin.Context) {
	orgID := c.Param("orgId")
	apiKeyID := c.Param("id")

	// Validate IDs
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	apiKeyUUID, err := uuid.Parse(apiKeyID)
	if err != nil {
		utils.BadRequest(c, "Invalid API key ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Parse date range
	days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
	if days < 1 || days > 365 {
		days = 30
	}

	db := database.GetDB()

	// Check access
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
		orgUUID, userUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Check if API key exists
	var apiKey models.APIKey
	if err := db.First(&apiKey, "id = ? AND organization_id = ?", apiKeyUUID, orgUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "API key not found")
			return
		}
		utils.InternalError(c, "Failed to fetch API key")
		return
	}

	// Get daily usage statistics
	dailyUsage := h.getDailyUsageStats(db, apiKeyUUID, days)

	utils.Success(c, dailyUsage)
}

// Helper methods for calculating statistics

func (h *APIKeyUsageHandler) getAPIKeyUsageStats(db *gorm.DB, apiKeyID uuid.UUID, fromDate, toDate time.Time) APIKeyUsageResponse {
	// In a real implementation, you would query the API call logs table
	// For now, we'll return mock data since we don't have the API call logs table implemented
	
	return APIKeyUsageResponse{
		TotalCalls:      1250,
		SuccessfulCalls: 1180,
		ErrorCalls:      70,
		SuccessRate:     94.4,
		LastUsed:        &[]string{utils.FormatTimestamp(utils.GetCurrentTime().Add(-2 * time.Hour))}[0],
		ByEndpoint: []EndpointUsage{
			{Endpoint: "/api/translations", Method: "GET", CallCount: 450, SuccessRate: 96.2, AvgResponseTime: 120.5},
			{Endpoint: "/api/translations", Method: "POST", CallCount: 320, SuccessRate: 92.8, AvgResponseTime: 180.3},
			{Endpoint: "/api/projects", Method: "GET", CallCount: 280, SuccessRate: 98.1, AvgResponseTime: 95.2},
			{Endpoint: "/api/ai/translate", Method: "POST", CallCount: 200, SuccessRate: 89.5, AvgResponseTime: 2500.0},
		},
		ByStatusCode: []StatusCodeUsage{
			{StatusCode: 200, Count: 1050, Percentage: 84.0},
			{StatusCode: 201, Count: 130, Percentage: 10.4},
			{StatusCode: 400, Count: 35, Percentage: 2.8},
			{StatusCode: 401, Count: 20, Percentage: 1.6},
			{StatusCode: 500, Count: 15, Percentage: 1.2},
		},
	}
}

func (h *APIKeyUsageHandler) getEndpointUsageStats(db *gorm.DB, apiKeyID uuid.UUID, page, limit int) []EndpointUsage {
	// Mock data for endpoint usage
	return []EndpointUsage{
		{Endpoint: "/api/translations", Method: "GET", CallCount: 450, SuccessRate: 96.2, AvgResponseTime: 120.5},
		{Endpoint: "/api/translations", Method: "POST", CallCount: 320, SuccessRate: 92.8, AvgResponseTime: 180.3},
		{Endpoint: "/api/projects", Method: "GET", CallCount: 280, SuccessRate: 98.1, AvgResponseTime: 95.2},
		{Endpoint: "/api/ai/translate", Method: "POST", CallCount: 200, SuccessRate: 89.5, AvgResponseTime: 2500.0},
		{Endpoint: "/api/locales", Method: "GET", CallCount: 150, SuccessRate: 99.3, AvgResponseTime: 85.1},
	}
}

func (h *APIKeyUsageHandler) getDailyUsageStats(db *gorm.DB, apiKeyID uuid.UUID, days int) []DailyUsage {
	// Mock data for daily usage
	var dailyUsage []DailyUsage
	now := utils.GetCurrentTime()
	
	for i := days - 1; i >= 0; i-- {
		date := now.AddDate(0, 0, -i)
		dailyUsage = append(dailyUsage, DailyUsage{
			Date:        date.Format("2006-01-02"),
			CallCount:   int64(40 + i*2), // Mock increasing usage
			SuccessRate: 94.0 + float64(i%5), // Mock varying success rate
		})
	}
	
	return dailyUsage
}
