package handlers

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// APIKeyHandler handles API key-related requests
type APIKeyHandler struct{}

// NewAPIKeyHandler creates a new API key handler
func NewAPIKeyHandler() *APIKeyHandler {
	return &APIKeyHandler{}
}

// CreateAPIKeyRequest represents the request structure for creating an API key
type CreateAPIKeyRequest struct {
	Name        string     `json:"name" binding:"required"`
	Permissions []string   `json:"permissions" binding:"required"`
	ExpiresAt   *time.Time `json:"expires_at"`
}

// UpdateAPIKeyRequest represents the request structure for updating an API key
type UpdateAPIKeyRequest struct {
	Name      *string    `json:"name"`
	IsActive  *bool      `json:"is_active"`
	ExpiresAt *time.Time `json:"expires_at"`
}

// APIKeyResponse represents the response structure for API key data
type APIKeyResponse struct {
	ID             string  `json:"id"`
	Name           string  `json:"name"`
	Key            *string `json:"key,omitempty"` // Only included when creating
	KeyPrefix      string  `json:"key_prefix"`
	OrganizationID string  `json:"organization_id"`
	IsActive       bool    `json:"is_active"`
	LastUsedAt     *string `json:"last_used_at"`
	ExpiresAt      *string `json:"expires_at"`
	CreatedAt      string  `json:"created_at"`
	UpdatedAt      string  `json:"updated_at"`
	CreatedBy      string  `json:"created_by"`
}

// generateAPIKey generates a new API key with prefix
func generateAPIKey() (string, string, error) {
	// Generate random bytes
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", "", err
	}

	// Create key with prefix
	keyData := hex.EncodeToString(bytes)
	prefix := keyData[:8]
	fullKey := fmt.Sprintf("adc_%s_%s", prefix, keyData[8:])

	return fullKey, prefix, nil
}

// hashAPIKey creates a SHA256 hash of the API key
func hashAPIKey(key string) string {
	hash := sha256.Sum256([]byte(key))
	return hex.EncodeToString(hash[:])
}

// toAPIKeyResponse converts an API key model to response format
func toAPIKeyResponse(apiKey models.APIKey, includeKey bool) APIKeyResponse {
	response := APIKeyResponse{
		ID:             apiKey.ID.String(),
		Name:           apiKey.Name,
		KeyPrefix:      strings.Split(apiKey.Key, "_")[1][:8] + "...",
		OrganizationID: apiKey.OrganizationID.String(),
		IsActive:       apiKey.IsActive,
		CreatedAt:      utils.FormatTimestamp(apiKey.CreatedAt),
		UpdatedAt:      utils.FormatTimestamp(apiKey.UpdatedAt),
		CreatedBy:      apiKey.CreatedBy.String(),
	}

	if apiKey.LastUsedAt != nil {
		lastUsed := utils.FormatTimestamp(*apiKey.LastUsedAt)
		response.LastUsedAt = &lastUsed
	}

	if apiKey.ExpiresAt != nil {
		expires := utils.FormatTimestamp(*apiKey.ExpiresAt)
		response.ExpiresAt = &expires
	}

	return response
}

// ListAPIKeys handles GET /api/organizations/:orgId/api-keys
func (h *APIKeyHandler) ListAPIKeys(c *gin.Context) {
	db := database.GetDB()
	orgID := c.Param("orgId")

	// Parse organization UUID
	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ?", organizationID, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Parse filter parameters
	isActive := c.Query("is_active")
	search := c.Query("search")

	// Build query
	query := db.Model(&models.APIKey{}).Where("organization_id = ?", organizationID)

	// Apply filters
	if isActive != "" {
		if isActive == "true" {
			query = query.Where("is_active = ?", true)
		} else if isActive == "false" {
			query = query.Where("is_active = ?", false)
		}
	}

	if search != "" {
		query = query.Where("name ILIKE ?", "%"+search+"%")
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		utils.InternalError(c, "Failed to count API keys")
		return
	}

	// Get API keys with pagination
	var apiKeys []models.APIKey
	offset := (page - 1) * limit
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&apiKeys).Error; err != nil {
		utils.InternalError(c, "Failed to fetch API keys")
		return
	}

	// Convert to response format
	var responses []APIKeyResponse
	for _, apiKey := range apiKeys {
		responses = append(responses, toAPIKeyResponse(apiKey, false))
	}

	// Create pagination response
	utils.SuccessWithPagination(c, responses, uint32(page), uint32(limit), uint64(total))
}

// GetAPIKey handles GET /api/organizations/:orgId/api-keys/:id
func (h *APIKeyHandler) GetAPIKey(c *gin.Context) {
	db := database.GetDB()
	orgID := c.Param("orgId")
	apiKeyID := c.Param("id")

	// Parse UUIDs
	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	id, err := uuid.Parse(apiKeyID)
	if err != nil {
		utils.BadRequest(c, "Invalid API key ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ?", organizationID, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Find API key
	var apiKey models.APIKey
	if err := db.First(&apiKey, "id = ? AND organization_id = ?", id, organizationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "API key not found")
			return
		}
		utils.InternalError(c, "Failed to fetch API key")
		return
	}

	utils.Success(c, toAPIKeyResponse(apiKey, false))
}

// CreateAPIKey handles POST /api/organizations/:orgId/api-keys
func (h *APIKeyHandler) CreateAPIKey(c *gin.Context) {
	db := database.GetDB()
	orgID := c.Param("orgId")
	var req CreateAPIKeyRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Parse organization UUID
	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ?", organizationID, userUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Generate API key
	fullKey, _, err := generateAPIKey()
	if err != nil {
		utils.InternalError(c, "Failed to generate API key")
		return
	}

	// Hash the key for storage
	hashedKey := hashAPIKey(fullKey)

	// Create API key
	apiKey := models.APIKey{
		Name:           req.Name,
		Key:            hashedKey,
		OrganizationID: organizationID,
		IsActive:       true,
		ExpiresAt:      req.ExpiresAt,
		CreatedBy:      userUUID,
	}

	if err := db.Create(&apiKey).Error; err != nil {
		utils.InternalError(c, "Failed to create API key")
		return
	}

	// Return response with the actual key (only time it's shown)
	response := toAPIKeyResponse(apiKey, true)
	response.Key = &fullKey

	utils.SuccessWithMessage(c, response, "API key created successfully")
}

// UpdateAPIKey handles PUT /api/organizations/:orgId/api-keys/:id
func (h *APIKeyHandler) UpdateAPIKey(c *gin.Context) {
	db := database.GetDB()
	orgID := c.Param("orgId")
	apiKeyID := c.Param("id")
	var req UpdateAPIKeyRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Parse UUIDs
	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	id, err := uuid.Parse(apiKeyID)
	if err != nil {
		utils.BadRequest(c, "Invalid API key ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ?", organizationID, userUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Find API key
	var apiKey models.APIKey
	if err := db.First(&apiKey, "id = ? AND organization_id = ?", id, organizationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "API key not found")
			return
		}
		utils.InternalError(c, "Failed to fetch API key")
		return
	}

	// Update fields
	if req.Name != nil {
		apiKey.Name = *req.Name
	}
	if req.IsActive != nil {
		apiKey.IsActive = *req.IsActive
	}
	if req.ExpiresAt != nil {
		apiKey.ExpiresAt = req.ExpiresAt
	}

	// Save changes
	if err := db.Save(&apiKey).Error; err != nil {
		utils.InternalError(c, "Failed to update API key")
		return
	}

	utils.SuccessWithMessage(c, toAPIKeyResponse(apiKey, false), "API key updated successfully")
}

// DeleteAPIKey handles DELETE /api/organizations/:orgId/api-keys/:id
func (h *APIKeyHandler) DeleteAPIKey(c *gin.Context) {
	db := database.GetDB()
	orgID := c.Param("orgId")
	apiKeyID := c.Param("id")

	// Parse UUIDs
	organizationID, err := uuid.Parse(orgID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization ID format")
		return
	}

	id, err := uuid.Parse(apiKeyID)
	if err != nil {
		utils.BadRequest(c, "Invalid API key ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	// Check if user has access to the organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ?", organizationID, userUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this organization")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Find API key
	var apiKey models.APIKey
	if err := db.First(&apiKey, "id = ? AND organization_id = ?", id, organizationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "API key not found")
			return
		}
		utils.InternalError(c, "Failed to fetch API key")
		return
	}

	// Delete API key
	if err := db.Delete(&apiKey).Error; err != nil {
		utils.InternalError(c, "Failed to delete API key")
		return
	}

	utils.SuccessWithMessage(c, nil, "API key deleted successfully")
}
