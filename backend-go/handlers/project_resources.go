package handlers

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// ProjectResourcesHandler handles project resources-related requests
type ProjectResourcesHandler struct{}

// NewProjectResourcesHandler creates a new project resources handler
func NewProjectResourcesHandler() *ProjectResourcesHandler {
	return &ProjectResourcesHandler{}
}

// ProjectLocaleRequest represents the request structure for adding a locale to a project
type ProjectLocaleRequest struct {
	LocaleCode string `json:"locale_code" binding:"required"`
}

// ProjectLocaleResourceResponse represents the response structure for project locales
type ProjectLocaleResourceResponse struct {
	ID               string  `json:"id"`
	Code             string  `json:"code"`
	Name             string  `json:"name"`
	NativeName       string  `json:"native_name"`
	Direction        string  `json:"direction"`
	IsActive         bool    `json:"is_active"`
	AddedAt          string  `json:"added_at"`
	TranslationCount int     `json:"translation_count"`
	CompletionRate   float64 `json:"completion_rate"`
}

// ProjectResourceResponse represents the response structure for project resources
type ProjectResourceResponse struct {
	ProjectID         string                          `json:"project_id"`
	ProjectName       string                          `json:"project_name"`
	ProjectSlug       string                          `json:"project_slug"`
	Locales           []ProjectLocaleResourceResponse `json:"locales"`
	TotalKeys         int                             `json:"total_keys"`
	TotalTranslations int                             `json:"total_translations"`
	CompletionRate    float64                         `json:"completion_rate"`
}

// GetProjectLocales handles GET /api/projects/:id/locales
func (h *ProjectResourcesHandler) GetProjectLocales(c *gin.Context) {
	projectID := c.Param("id")

	// Validate project ID
	projectUUID, err := uuid.Parse(projectID)
	if err != nil {
		utils.BadRequest(c, "Invalid project ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Get project and check access
	var project models.Project
	if err := db.First(&project, "id = ?", projectUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Project not found")
			return
		}
		utils.InternalError(c, "Failed to fetch project")
		return
	}

	// Check if user has access to the project's organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
		project.OrganizationID, userUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this project")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Get project locales through project_locales table
	var projectLocales []models.ProjectLocale
	if err := db.Preload("Locale").Where("project_id = ?", projectUUID).Find(&projectLocales).Error; err != nil {
		utils.InternalError(c, "Failed to fetch project locales")
		return
	}

	// Convert to response format with statistics
	var localeResponses []ProjectLocaleResourceResponse
	for _, pl := range projectLocales {
		// Calculate translation statistics for this locale
		translationCount := h.getTranslationCount(db, projectUUID, pl.LocaleID)
		totalKeys := h.getTotalKeysCount(db, projectUUID)
		completionRate := float64(0)
		if totalKeys > 0 {
			completionRate = float64(translationCount) / float64(totalKeys) * 100
		}

		localeResponse := ProjectLocaleResourceResponse{
			ID:               pl.Locale.ID.String(),
			Code:             pl.Locale.Code,
			Name:             pl.Locale.Name,
			NativeName:       pl.Locale.NativeName,
			Direction:        pl.Locale.Direction,
			IsActive:         pl.Locale.IsActive,
			AddedAt:          utils.FormatTimestamp(pl.CreatedAt),
			TranslationCount: translationCount,
			CompletionRate:   completionRate,
		}

		localeResponses = append(localeResponses, localeResponse)
	}

	utils.Success(c, localeResponses)
}

// AddProjectLocale handles POST /api/projects/:id/locales
func (h *ProjectResourcesHandler) AddProjectLocale(c *gin.Context) {
	projectID := c.Param("id")

	// Validate project ID
	projectUUID, err := uuid.Parse(projectID)
	if err != nil {
		utils.BadRequest(c, "Invalid project ID format")
		return
	}

	var req ProjectLocaleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Get project and check access
	var project models.Project
	if err := db.First(&project, "id = ?", projectUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Project not found")
			return
		}
		utils.InternalError(c, "Failed to fetch project")
		return
	}

	// Check if user has admin access to the project's organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ? AND (role = ? OR role = ?)",
		project.OrganizationID, userUUID, true, "admin", "editor").Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have permission to modify this project")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Check if locale exists
	var locale models.Locale
	if err := db.First(&locale, "code = ? AND is_active = ?", req.LocaleCode, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Locale not found or inactive")
			return
		}
		utils.InternalError(c, "Failed to fetch locale")
		return
	}

	// Check if locale is already added to the project
	var existingProjectLocale models.ProjectLocale
	if err := db.First(&existingProjectLocale, "project_id = ? AND locale_id = ?", projectUUID, locale.ID).Error; err == nil {
		utils.BadRequest(c, "Locale is already added to this project")
		return
	} else if err != gorm.ErrRecordNotFound {
		utils.InternalError(c, "Failed to check existing project locale")
		return
	}

	// Add locale to project
	projectLocale := models.ProjectLocale{
		ProjectID: projectUUID,
		LocaleID:  locale.ID,
	}

	if err := db.Create(&projectLocale).Error; err != nil {
		utils.InternalError(c, "Failed to add locale to project")
		return
	}

	// Return the added locale with statistics
	translationCount := h.getTranslationCount(db, projectUUID, locale.ID)
	totalKeys := h.getTotalKeysCount(db, projectUUID)
	completionRate := float64(0)
	if totalKeys > 0 {
		completionRate = float64(translationCount) / float64(totalKeys) * 100
	}

	response := ProjectLocaleResourceResponse{
		ID:               locale.ID.String(),
		Code:             locale.Code,
		Name:             locale.Name,
		NativeName:       locale.NativeName,
		Direction:        locale.Direction,
		IsActive:         locale.IsActive,
		AddedAt:          utils.FormatTimestamp(projectLocale.CreatedAt),
		TranslationCount: translationCount,
		CompletionRate:   completionRate,
	}

	utils.SuccessWithMessage(c, response, "Locale added to project successfully")
}

// GetProjectResources handles GET /api/projects/:id/resources
func (h *ProjectResourcesHandler) GetProjectResources(c *gin.Context) {
	projectID := c.Param("id")

	// Validate project ID
	projectUUID, err := uuid.Parse(projectID)
	if err != nil {
		utils.BadRequest(c, "Invalid project ID format")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		utils.InternalError(c, "Invalid user ID")
		return
	}

	db := database.GetDB()

	// Get project and check access
	var project models.Project
	if err := db.First(&project, "id = ?", projectUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Project not found")
			return
		}
		utils.InternalError(c, "Failed to fetch project")
		return
	}

	// Check if user has access to the project's organization
	var membership models.OrganizationMembership
	if err := db.First(&membership, "organization_id = ? AND user_id = ? AND is_active = ?",
		project.OrganizationID, userUUID, true).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "User does not have access to this project")
			return
		}
		utils.InternalError(c, "Failed to check organization membership")
		return
	}

	// Get project locales
	var projectLocales []models.ProjectLocale
	if err := db.Preload("Locale").Where("project_id = ?", projectUUID).Find(&projectLocales).Error; err != nil {
		utils.InternalError(c, "Failed to fetch project locales")
		return
	}

	// Calculate overall statistics
	totalKeys := h.getTotalKeysCount(db, projectUUID)
	totalTranslations := h.getTotalTranslationsCount(db, projectUUID)
	overallCompletionRate := float64(0)
	if totalKeys > 0 && len(projectLocales) > 0 {
		expectedTranslations := totalKeys * len(projectLocales)
		overallCompletionRate = float64(totalTranslations) / float64(expectedTranslations) * 100
	}

	// Convert locales to response format
	var localeResponses []ProjectLocaleResourceResponse
	for _, pl := range projectLocales {
		translationCount := h.getTranslationCount(db, projectUUID, pl.LocaleID)
		completionRate := float64(0)
		if totalKeys > 0 {
			completionRate = float64(translationCount) / float64(totalKeys) * 100
		}

		localeResponse := ProjectLocaleResourceResponse{
			ID:               pl.Locale.ID.String(),
			Code:             pl.Locale.Code,
			Name:             pl.Locale.Name,
			NativeName:       pl.Locale.NativeName,
			Direction:        pl.Locale.Direction,
			IsActive:         pl.Locale.IsActive,
			AddedAt:          utils.FormatTimestamp(pl.CreatedAt),
			TranslationCount: translationCount,
			CompletionRate:   completionRate,
		}

		localeResponses = append(localeResponses, localeResponse)
	}

	response := ProjectResourceResponse{
		ProjectID:         project.ID.String(),
		ProjectName:       project.Name,
		ProjectSlug:       project.Slug,
		Locales:           localeResponses,
		TotalKeys:         totalKeys,
		TotalTranslations: totalTranslations,
		CompletionRate:    overallCompletionRate,
	}

	utils.Success(c, response)
}

// Helper methods for calculating statistics

func (h *ProjectResourcesHandler) getTranslationCount(db *gorm.DB, projectID, localeID uuid.UUID) int {
	var count int64
	db.Model(&models.Translation{}).
		Joins("JOIN translation_keys ON translations.translation_key_id = translation_keys.id").
		Where("translation_keys.project_id = ? AND translations.locale_id = ?", projectID, localeID).
		Count(&count)
	return int(count)
}

func (h *ProjectResourcesHandler) getTotalKeysCount(db *gorm.DB, projectID uuid.UUID) int {
	var count int64
	db.Model(&models.TranslationKey{}).Where("project_id = ?", projectID).Count(&count)
	return int(count)
}

func (h *ProjectResourcesHandler) getTotalTranslationsCount(db *gorm.DB, projectID uuid.UUID) int {
	var count int64
	db.Model(&models.Translation{}).
		Joins("JOIN translation_keys ON translations.translation_key_id = translation_keys.id").
		Where("translation_keys.project_id = ?", projectID).
		Count(&count)
	return int(count)
}
