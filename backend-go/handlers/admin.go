package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"
)

// AdminHandler handles admin-related requests
type AdminHandler struct{}

// NewAdminHandler creates a new admin handler
func NewAdminHandler() *AdminHandler {
	return &AdminHandler{}
}

// DashboardStats represents dashboard statistics
type DashboardStats struct {
	TotalUsers         int64 `json:"total_users"`
	TotalOrganizations int64 `json:"total_organizations"`
	TotalProjects      int64 `json:"total_projects"`
	TotalTranslations  int64 `json:"total_translations"`
	ActiveUsers        int64 `json:"active_users"`
	NewUsersThisMonth  int64 `json:"new_users_this_month"`
}

// UserListResponse represents a user in admin list
type UserListResponse struct {
	ID                string  `json:"id"`
	Email             string  `json:"email"`
	Username          *string `json:"username"`
	FirstName         *string `json:"first_name"`
	LastName          *string `json:"last_name"`
	EmailVerified     bool    `json:"email_verified"`
	IsActive          bool    `json:"is_active"`
	CreatedAt         string  `json:"created_at"`
	LastLoginAt       *string `json:"last_login_at"`
	OrganizationCount int64   `json:"organization_count"`
}

// toUserListResponse converts a user model to admin list response format
func toUserListResponse(user models.User, orgCount int64) UserListResponse {
	response := UserListResponse{
		ID:                user.ID.String(),
		Email:             user.Email,
		Username:          user.Username,
		FirstName:         user.FirstName,
		LastName:          user.LastName,
		EmailVerified:     user.EmailVerified,
		IsActive:          user.IsActive,
		CreatedAt:         utils.FormatTimestamp(user.CreatedAt),
		OrganizationCount: orgCount,
	}

	if user.LastLoginAt != nil {
		lastLogin := utils.FormatTimestamp(*user.LastLoginAt)
		response.LastLoginAt = &lastLogin
	}

	return response
}

// checkAdminAccess checks if the user has admin access
func (h *AdminHandler) checkAdminAccess(c *gin.Context) bool {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Unauthorized(c, "User not authenticated")
		return false
	}

	// For now, we'll implement a simple check
	// In a real application, you'd check against admin roles or permissions
	db := database.GetDB()
	var user models.User
	if err := db.First(&user, "id = ?", userID).Error; err != nil {
		utils.Forbidden(c, "Access denied")
		return false
	}

	// Simple check: if user email contains "admin" or is a specific admin email
	// In production, you'd have a proper admin role system
	if user.Email == "<EMAIL>" || user.Email == "<EMAIL>" {
		return true
	}

	utils.Forbidden(c, "Admin access required")
	return false
}

// Dashboard handles GET /api/admin/dashboard
func (h *AdminHandler) Dashboard(c *gin.Context) {
	if !h.checkAdminAccess(c) {
		return
	}

	db := database.GetDB()

	// Get dashboard statistics
	var stats DashboardStats

	// Count total users
	db.Model(&models.User{}).Count(&stats.TotalUsers)

	// Count total organizations
	db.Model(&models.Organization{}).Count(&stats.TotalOrganizations)

	// Count total projects
	db.Model(&models.Project{}).Count(&stats.TotalProjects)

	// Count total translations
	db.Model(&models.Translation{}).Count(&stats.TotalTranslations)

	// Count active users (users who have logged in within the last 30 days)
	db.Model(&models.User{}).Where("last_login_at > NOW() - INTERVAL '30 days'").Count(&stats.ActiveUsers)

	// Count new users this month
	db.Model(&models.User{}).Where("created_at > DATE_TRUNC('month', NOW())").Count(&stats.NewUsersThisMonth)

	utils.Success(c, stats)
}

// ListUsers handles GET /api/admin/users
func (h *AdminHandler) ListUsers(c *gin.Context) {
	if !h.checkAdminAccess(c) {
		return
	}

	db := database.GetDB()

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Parse filter parameters
	search := c.Query("search")
	isActive := c.Query("is_active")
	emailVerified := c.Query("email_verified")

	// Build query
	query := db.Model(&models.User{})

	if search != "" {
		query = query.Where("email ILIKE ? OR first_name ILIKE ? OR last_name ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if isActive != "" {
		if isActive == "true" {
			query = query.Where("is_active = ?", true)
		} else if isActive == "false" {
			query = query.Where("is_active = ?", false)
		}
	}

	if emailVerified != "" {
		if emailVerified == "true" {
			query = query.Where("email_verified = ?", true)
		} else if emailVerified == "false" {
			query = query.Where("email_verified = ?", false)
		}
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		utils.InternalError(c, "Failed to count users")
		return
	}

	// Get users with pagination
	var users []models.User
	offset := (page - 1) * limit
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		utils.InternalError(c, "Failed to fetch users")
		return
	}

	// Get organization counts for each user
	var responses []UserListResponse
	for _, user := range users {
		var orgCount int64
		db.Model(&models.OrganizationMembership{}).Where("user_id = ? AND is_active = ?", user.ID, true).Count(&orgCount)
		responses = append(responses, toUserListResponse(user, orgCount))
	}

	utils.SuccessWithPagination(c, responses, uint32(page), uint32(limit), uint64(total))
}

// GetUser handles GET /api/admin/users/:id
func (h *AdminHandler) GetUser(c *gin.Context) {
	if !h.checkAdminAccess(c) {
		return
	}

	db := database.GetDB()
	userID := c.Param("id")

	// Parse UUID
	id, err := uuid.Parse(userID)
	if err != nil {
		utils.BadRequest(c, "Invalid user ID format")
		return
	}

	// Find user
	var user models.User
	if err := db.First(&user, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "User not found")
			return
		}
		utils.InternalError(c, "Failed to fetch user")
		return
	}

	// Get organization count
	var orgCount int64
	db.Model(&models.OrganizationMembership{}).Where("user_id = ? AND is_active = ?", user.ID, true).Count(&orgCount)

	utils.Success(c, toUserListResponse(user, orgCount))
}

// UpdateUserStatus handles PUT /api/admin/users/:id/status
func (h *AdminHandler) UpdateUserStatus(c *gin.Context) {
	if !h.checkAdminAccess(c) {
		return
	}

	db := database.GetDB()
	userID := c.Param("id")

	var req struct {
		IsActive *bool `json:"is_active"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Parse UUID
	id, err := uuid.Parse(userID)
	if err != nil {
		utils.BadRequest(c, "Invalid user ID format")
		return
	}

	// Find user
	var user models.User
	if err := db.First(&user, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "User not found")
			return
		}
		utils.InternalError(c, "Failed to fetch user")
		return
	}

	// Update status
	if req.IsActive != nil {
		user.IsActive = *req.IsActive
	}

	// Save changes
	if err := db.Save(&user).Error; err != nil {
		utils.InternalError(c, "Failed to update user")
		return
	}

	// Get organization count
	var orgCount int64
	db.Model(&models.OrganizationMembership{}).Where("user_id = ? AND is_active = ?", user.ID, true).Count(&orgCount)

	utils.SuccessWithMessage(c, toUserListResponse(user, orgCount), "User status updated successfully")
}
