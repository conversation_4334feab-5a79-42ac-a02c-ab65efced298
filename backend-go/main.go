package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"adc-multi-languages/config"
	"adc-multi-languages/database"
	"adc-multi-languages/models"
	"adc-multi-languages/routes"
	"adc-multi-languages/services"
	"adc-multi-languages/utils"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	utils.InitLogger(cfg.LogLevel)
	utils.Logger.Info("Starting ADC Multi-Languages API server")

	// Connect to database
	dbConfig := database.Config{
		Host:     cfg.DatabaseHost,
		Port:     cfg.DatabasePort,
		User:     cfg.DatabaseUser,
		Password: cfg.DatabasePassword,
		DBName:   cfg.DatabaseName,
		SSLMode:  cfg.DatabaseSSLMode,
		URL:      cfg.DatabaseURL,
	}

	if err := database.Connect(dbConfig); err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer database.Close()

	// Run database migrations
	if err := runMigrations(); err != nil {
		log.Fatalf("Failed to run database migrations: %v", err)
	}

	// Initialize services
	services.GetCacheService()
	services.GetMetricsService()
	services.GetBackgroundJobService()

	// Setup routes
	router := routes.SetupRoutes(cfg)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%s", cfg.Host, cfg.Port),
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// Start server in a goroutine
	go func() {
		utils.Logger.WithField("address", server.Addr).Info("Server starting")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	utils.Logger.Info("Server shutting down...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	utils.Logger.Info("Server exited")
}

// runMigrations runs database migrations
func runMigrations() error {
	utils.Logger.Info("Running database migrations...")

	// Auto-migrate all models
	err := database.Migrate(
		&models.User{},
		&models.Organization{},
		&models.OrganizationMembership{},
		&models.Project{},
		&models.ProjectLocale{},
		&models.Resource{},
		&models.Locale{},
		&models.TranslationKey{},
		&models.Translation{},
		&models.TranslationHistory{},
		&models.APIKey{},
		&models.APICallLog{},
		&models.PermissionAuditLog{},
		&models.PermissionGroup{},
		&models.AICreditTransaction{},
		&models.AICreditUsage{},
		&models.SubscriptionPlan{},
		&models.OrganizationSubscription{},
		&models.PasswordResetToken{},
		&models.EmailVerificationToken{},
	)

	if err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	utils.Logger.Info("Database migrations completed successfully")
	return nil
}
