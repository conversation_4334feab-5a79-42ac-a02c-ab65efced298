version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: adc-postgres
    environment:
      POSTGRES_DB: adc_multi_languages
      POSTGRES_USER: adc_user
      POSTGRES_PASSWORD: adc_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - adc-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U adc_user -d adc_multi_languages"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis (optional - for external caching)
  redis:
    image: redis:7-alpine
    container_name: adc-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - adc-network
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # ADC Backend Application
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: adc-backend
    environment:
      # Database
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: adc_user
      DB_PASSWORD: adc_password
      DB_NAME: adc_multi_languages
      DB_SSL_MODE: disable
      
      # Server
      PORT: 8080
      GIN_MODE: release
      BASE_URL: http://localhost:8080
      
      # JWT
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-change-this-in-production
      JWT_EXPIRY: 24h
      JWT_REFRESH_EXPIRY: 168h
      
      # Frontend
      FRONTEND_URL: http://localhost:3000
      SUPPORT_EMAIL: <EMAIL>
      
      # Email (configure with your SMTP settings)
      SMTP_HOST: smtp.gmail.com
      SMTP_PORT: 587
      SMTP_USERNAME: ${SMTP_USERNAME}
      SMTP_PASSWORD: ${SMTP_PASSWORD}
      FROM_EMAIL: <EMAIL>
      FROM_NAME: ADC Multi-Languages
      
      # AI Translation (add your API keys)
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      GOOGLE_TRANSLATE_API_KEY: ${GOOGLE_TRANSLATE_API_KEY}
      AZURE_TRANSLATOR_API_KEY: ${AZURE_TRANSLATOR_API_KEY}
      DEFAULT_TRANSLATION_PROVIDER: openai
      
      # File Upload
      UPLOAD_DIR: ./uploads
      MAX_FILE_SIZE: 10485760
      
      # Stripe (add your keys)
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET}
      
      # Features
      ENABLE_AI_TRANSLATION: true
      ENABLE_FILE_UPLOAD: true
      ENABLE_EMAIL_VERIFICATION: true
      ENABLE_RATE_LIMITING: true
      ENABLE_CACHING: true
      
      # Redis (optional)
      REDIS_HOST: redis
      REDIS_PORT: 6379
      
    ports:
      - "8080:8080"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - adc-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx (optional - for reverse proxy)
  nginx:
    image: nginx:alpine
    container_name: adc-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./uploads:/var/www/uploads:ro
    networks:
      - adc-network
    depends_on:
      - backend
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  adc-network:
    driver: bridge
