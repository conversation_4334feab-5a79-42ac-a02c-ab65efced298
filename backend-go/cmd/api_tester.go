package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
)

// SimpleTestRunner provides basic API testing functionality
type SimpleTestRunner struct {
	baseURL string
	client  *http.Client
}

// NewSimpleTestRunner creates a new simple test runner
func NewSimpleTestRunner() *SimpleTestRunner {
	return &SimpleTestRunner{
		baseURL: "http://localhost:8300",
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// TestResult represents the result of a test
type TestResult struct {
	Name     string
	Method   string
	URL      string
	Expected int
	Actual   int
	Success  bool
	Error    string
	Response map[string]interface{}
}

// makeRequest makes an HTTP request and returns the response
func (tr *SimpleTestRunner) makeRequest(method, endpoint string, body interface{}, headers map[string]string) (*http.Response, error) {
	var reqBody io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		reqBody = bytes.NewBuffer(jsonBody)
	}

	url := tr.baseURL + endpoint
	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, err
	}

	// Set default headers
	req.Header.Set("Content-Type", "application/json")

	// Set custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	return tr.client.Do(req)
}

// RunBasicTests runs basic API tests
func (tr *SimpleTestRunner) RunBasicTests() []TestResult {
	var results []TestResult

	tests := []struct {
		name     string
		method   string
		endpoint string
		body     interface{}
		headers  map[string]string
		expected int
	}{
		{
			name:     "Health Check",
			method:   "GET",
			endpoint: "/health",
			expected: 200,
		},
		{
			name:     "Get Locales",
			method:   "GET",
			endpoint: "/api/locales",
			expected: 200,
		},
		{
			name:     "Get Subscription Plans",
			method:   "GET",
			endpoint: "/api/subscription-plans",
			expected: 200,
		},
		{
			name:     "AI Credits Pricing",
			method:   "GET",
			endpoint: "/api/ai-credits/pricing",
			expected: 200,
		},
		{
			name:     "AI Credits Pricing Calculation",
			method:   "GET",
			endpoint: "/api/ai-credits/pricing/calculate?amount=1000",
			expected: 200,
		},
		{
			name:     "Audit Log Actions",
			method:   "GET",
			endpoint: "/api/audit-logs/actions",
			expected: 200,
		},
		{
			name:     "Audit Log Resource Types",
			method:   "GET",
			endpoint: "/api/audit-logs/resource-types",
			expected: 200,
		},
		{
			name:     "Sign Up",
			method:   "POST",
			endpoint: "/api/auth/signup",
			body: map[string]interface{}{
				"email":      "<EMAIL>",
				"password":   "password123",
				"first_name": "Test",
				"last_name":  "User",
			},
			expected: 201,
		},
		{
			name:     "Sign In",
			method:   "POST",
			endpoint: "/api/auth/signin",
			body: map[string]interface{}{
				"email":    "<EMAIL>",
				"password": "password123",
			},
			expected: 200,
		},
	}

	for _, test := range tests {
		resp, err := tr.makeRequest(test.method, test.endpoint, test.body, test.headers)

		result := TestResult{
			Name:     test.name,
			Method:   test.method,
			URL:      tr.baseURL + test.endpoint,
			Expected: test.expected,
		}

		if err != nil {
			result.Error = err.Error()
			result.Success = false
		} else {
			result.Actual = resp.StatusCode
			result.Success = resp.StatusCode == test.expected

			// Try to parse response
			if resp.Body != nil {
				bodyBytes, err := io.ReadAll(resp.Body)
				resp.Body.Close()
				if err == nil {
					var response map[string]interface{}
					if err := json.Unmarshal(bodyBytes, &response); err == nil {
						result.Response = response
					}
				}
			}
		}

		results = append(results, result)
	}

	return results
}

// PrintResults prints test results in a formatted way
func (tr *SimpleTestRunner) PrintResults(results []TestResult) {
	separator := strings.Repeat("=", 80)

	fmt.Println("\n" + separator)
	fmt.Println("API INTEGRATION TEST RESULTS")
	fmt.Println(separator)

	passed := 0
	failed := 0

	for _, result := range results {
		status := "✅ PASS"
		if !result.Success {
			status = "❌ FAIL"
			failed++
		} else {
			passed++
		}

		fmt.Printf("\n%s | %s %s\n", status, result.Method, result.URL)
		fmt.Printf("   Expected: %d, Got: %d\n", result.Expected, result.Actual)

		if result.Error != "" {
			fmt.Printf("   Error: %s\n", result.Error)
		}

		if result.Response != nil {
			if success, ok := result.Response["success"]; ok {
				fmt.Printf("   Response Success: %v\n", success)
			}
			if message, ok := result.Response["message"]; ok {
				fmt.Printf("   Message: %s\n", message)
			}
		}
	}

	fmt.Println("\n" + separator)
	fmt.Printf("SUMMARY: %d passed, %d failed, %d total\n", passed, failed, len(results))
	fmt.Println(separator)

	if failed > 0 {
		fmt.Printf("\n❌ %d tests failed. Check the errors above.\n", failed)
		fmt.Println("💡 Make sure the server is running on http://localhost:8300")
	} else {
		fmt.Printf("\n🎉 All %d tests passed!\n", passed)
	}
}

// CheckServerHealth checks if the server is running
func (tr *SimpleTestRunner) CheckServerHealth() bool {
	resp, err := tr.makeRequest("GET", "/health", nil, nil)
	if err != nil {
		fmt.Printf("❌ Server health check failed: %v\n", err)
		return false
	}
	if resp.StatusCode != 200 {
		fmt.Printf("❌ Server health check returned status: %d\n", resp.StatusCode)
		return false
	}
	return true
}

// RunTests is the main test runner function
func RunSimpleTests() {
	fmt.Println("🚀 Starting Simple API Integration Tests...")

	// Create test runner
	tr := NewSimpleTestRunner()

	// Check if server is running
	fmt.Println("🔍 Checking if server is running...")
	if !tr.CheckServerHealth() {
		fmt.Println("❌ Server is not running or not accessible at http://localhost:8300")
		fmt.Println("💡 Please start the server first with: go run main.go")
		return
	}

	fmt.Println("✅ Server is running")

	// Run basic tests
	fmt.Println("🧪 Running API tests...")
	results := tr.RunBasicTests()

	// Print results
	tr.PrintResults(results)
}

// Main function for running tests
func main() {
	if len(os.Args) > 1 && os.Args[1] == "test" {
		RunSimpleTests()
	} else {
		fmt.Println("Usage: go run api_tester.go test")
		fmt.Println("Make sure the server is running first on port 8300: go run main.go")
	}
}
