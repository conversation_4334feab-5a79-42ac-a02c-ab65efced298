package utils

import "time"

// GetCurrentTime returns the current time in UTC
func GetCurrentTime() time.Time {
	return time.Now().UTC()
}

// GetStartOfMonth returns the start of the month for the given time
func GetStartOfMonth(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
}

// GetEndOfMonth returns the end of the month for the given time
func GetEndOfMonth(t time.Time) time.Time {
	return GetStartOfMonth(t).AddDate(0, 1, 0).Add(-time.Nanosecond)
}

// GetStartOfDay returns the start of the day for the given time
func GetStartOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// GetEndOfDay returns the end of the day for the given time
func GetEndOfDay(t time.Time) time.Time {
	return GetStartOfDay(t).AddDate(0, 0, 1).Add(-time.Nanosecond)
}

// GetStartOfWeek returns the start of the week (Monday) for the given time
func GetStartOfWeek(t time.Time) time.Time {
	weekday := int(t.Weekday())
	if weekday == 0 {
		weekday = 7 // Sunday = 7
	}
	return GetStartOfDay(t).AddDate(0, 0, -(weekday - 1))
}

// GetEndOfWeek returns the end of the week (Sunday) for the given time
func GetEndOfWeek(t time.Time) time.Time {
	return GetStartOfWeek(t).AddDate(0, 0, 7).Add(-time.Nanosecond)
}

// ParseDateString parses a date string in YYYY-MM-DD format
func ParseDateString(dateStr string) (time.Time, error) {
	return time.Parse("2006-01-02", dateStr)
}

// ParseDateTimeString parses a datetime string in RFC3339 format
func ParseDateTimeString(dateTimeStr string) (time.Time, error) {
	return time.Parse(time.RFC3339, dateTimeStr)
}
