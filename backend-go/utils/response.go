package utils

import (
	"math"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// APIResponse represents the standard API response structure
type APIResponse struct {
	Success bool        `json:"success"`
	Status  int         `json:"status"`
	Message *string     `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Meta    *MetaInfo   `json:"meta,omitempty"`
}

// MetaInfo contains metadata about the response
type MetaInfo struct {
	Timestamp  time.Time       `json:"timestamp"`
	RequestID  *string         `json:"request_id,omitempty"`
	Version    *string         `json:"version,omitempty"`
	Pagination *PaginationInfo `json:"pagination,omitempty"`
}

// PaginationInfo contains pagination metadata
type PaginationInfo struct {
	Page       uint32 `json:"page"`
	PerPage    uint32 `json:"per_page"`
	Total      uint64 `json:"total"`
	TotalPages uint32 `json:"total_pages"`
}

// ErrorCode represents different types of errors
type ErrorCode string

const (
	ErrorCodeBadRequest        ErrorCode = "BAD_REQUEST"
	ErrorCodeUnauthorized      ErrorCode = "UNAUTHORIZED"
	ErrorCodeForbidden         ErrorCode = "FORBIDDEN"
	ErrorCodeNotFound          ErrorCode = "NOT_FOUND"
	ErrorCodeConflict          ErrorCode = "CONFLICT"
	ErrorCodeInternalError     ErrorCode = "INTERNAL_ERROR"
	ErrorCodeValidationError   ErrorCode = "VALIDATION_ERROR"
	ErrorCodePaymentRequired   ErrorCode = "PAYMENT_REQUIRED"
	ErrorCodeRateLimitExceeded ErrorCode = "RATE_LIMIT_EXCEEDED"
)

// FieldError represents a validation error for a specific field
type FieldError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// Success creates a successful response with data
func Success(c *gin.Context, data interface{}) {
	response := APIResponse{
		Success: true,
		Status:  http.StatusOK,
		Data:    data,
		Meta: &MetaInfo{
			Timestamp: time.Now().UTC(),
			RequestID: stringPtr(uuid.New().String()),
			Version:   stringPtr("1.0"),
		},
	}
	c.JSON(http.StatusOK, response)
}

// SuccessWithMessage creates a successful response with data and a custom message
func SuccessWithMessage(c *gin.Context, data interface{}, message string) {
	response := APIResponse{
		Success: true,
		Status:  http.StatusOK,
		Message: &message,
		Data:    data,
		Meta: &MetaInfo{
			Timestamp: time.Now().UTC(),
			RequestID: stringPtr(uuid.New().String()),
			Version:   stringPtr("1.0"),
		},
	}
	c.JSON(http.StatusOK, response)
}

// SuccessWithStatus creates a successful response with data and a custom status code
func SuccessWithStatus(c *gin.Context, data interface{}, status int) {
	response := APIResponse{
		Success: true,
		Status:  status,
		Data:    data,
		Meta: &MetaInfo{
			Timestamp: time.Now().UTC(),
			RequestID: stringPtr(uuid.New().String()),
			Version:   stringPtr("1.0"),
		},
	}
	c.JSON(status, response)
}

// SuccessWithPagination creates a successful response with pagination
func SuccessWithPagination(c *gin.Context, data interface{}, page, perPage uint32, total uint64) {
	totalPages := uint32(math.Ceil(float64(total) / float64(perPage)))

	response := APIResponse{
		Success: true,
		Status:  http.StatusOK,
		Data:    data,
		Meta: &MetaInfo{
			Timestamp: time.Now().UTC(),
			RequestID: stringPtr(uuid.New().String()),
			Version:   stringPtr("1.0"),
			Pagination: &PaginationInfo{
				Page:       page,
				PerPage:    perPage,
				Total:      total,
				TotalPages: totalPages,
			},
		},
	}
	c.JSON(http.StatusOK, response)
}

// Error creates an error response
func Error(c *gin.Context, status int, code ErrorCode, message string) {
	response := APIResponse{
		Success: false,
		Status:  status,
		Message: &message,
		Meta: &MetaInfo{
			Timestamp: time.Now().UTC(),
			RequestID: stringPtr(uuid.New().String()),
			Version:   stringPtr("1.0"),
		},
	}
	c.JSON(status, response)
}

// BadRequest creates a 400 Bad Request error response
func BadRequest(c *gin.Context, message string) {
	Error(c, http.StatusBadRequest, ErrorCodeBadRequest, message)
}

// Unauthorized creates a 401 Unauthorized error response
func Unauthorized(c *gin.Context, message string) {
	Error(c, http.StatusUnauthorized, ErrorCodeUnauthorized, message)
}

// Forbidden creates a 403 Forbidden error response
func Forbidden(c *gin.Context, message string) {
	Error(c, http.StatusForbidden, ErrorCodeForbidden, message)
}

// NotFound creates a 404 Not Found error response
func NotFound(c *gin.Context, message string) {
	Error(c, http.StatusNotFound, ErrorCodeNotFound, message)
}

// Conflict creates a 409 Conflict error response
func Conflict(c *gin.Context, message string) {
	Error(c, http.StatusConflict, ErrorCodeConflict, message)
}

// InternalServerError creates a 500 Internal Server Error response
func InternalServerError(c *gin.Context, message string) {
	Error(c, http.StatusInternalServerError, ErrorCodeInternalError, message)
}

// ValidationError creates a 400 Bad Request error response for validation errors
func ValidationError(c *gin.Context, fieldErrors []FieldError) {
	message := "Validation failed"
	Error(c, http.StatusBadRequest, ErrorCodeValidationError, message)
}

// PaymentRequired creates a 402 Payment Required error response
func PaymentRequired(c *gin.Context, message string) {
	Error(c, http.StatusPaymentRequired, ErrorCodePaymentRequired, message)
}

// RateLimitExceeded creates a 429 Too Many Requests error response
func RateLimitExceeded(c *gin.Context, message string) {
	Error(c, http.StatusTooManyRequests, ErrorCodeRateLimitExceeded, message)
}

// stringPtr returns a pointer to a string
func stringPtr(s string) *string {
	return &s
}

// GetCurrentTimestamp returns the current timestamp in UTC
func GetCurrentTimestamp() time.Time {
	return time.Now().UTC()
}

// FormatTimestamp formats a time.Time to ISO 8601 string
func FormatTimestamp(t time.Time) string {
	return t.UTC().Format(time.RFC3339)
}

// InternalError is an alias for InternalServerError for backward compatibility
func InternalError(c *gin.Context, message string) {
	InternalServerError(c, message)
}

// CreatePaginationMetadata creates pagination metadata for responses
func CreatePaginationMetadata(page, limit, total int) *PaginationInfo {
	totalPages := uint32(math.Ceil(float64(total) / float64(limit)))
	return &PaginationInfo{
		Page:       uint32(page),
		PerPage:    uint32(limit),
		Total:      uint64(total),
		TotalPages: totalPages,
	}
}
