package tests

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/stretchr/testify/assert"
)

// TestCreditLimitManagement tests credit limit CRUD operations
func (suite *IntegrationTestSuite) TestCreditLimitManagement() {
	// Test getting credit limit (should return default/empty initially)
	suite.T().Run("GetCreditLimit", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/credit-limit", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})

	// Test updating credit limit
	suite.T().Run("UpdateCreditLimit", func(t *testing.T) {
		creditLimitData := map[string]interface{}{
			"monthly_limit":         5000,
			"warning_threshold":     80,
			"auto_purchase_enabled": true,
			"auto_purchase_amount":  1000,
		}

		url := fmt.Sprintf("/api/organizations/%s/credit-limit", suite.orgID)
		w := suite.makeAuthenticatedRequest("PUT", url, creditLimitData)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(5000), data["monthly_limit"])
		assert.Equal(t, float64(80), data["warning_threshold"])
		assert.Equal(t, true, data["auto_purchase_enabled"])
	})
}

// TestCreditUsageAnalytics tests credit usage analytics endpoints
func (suite *IntegrationTestSuite) TestCreditUsageAnalytics() {
	// Test getting credit usage history
	suite.T().Run("GetCreditUsageHistory", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/credit-usage?page=1&per_page=10", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		// Should have pagination info
		assert.Contains(t, response, "pagination")
	})

	// Test getting credit analytics
	suite.T().Run("GetCreditAnalytics", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/credit-analytics?period=30d", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "total_credits_used")
		assert.Contains(t, data, "total_operations")
		assert.Contains(t, data, "by_operation")
		assert.Contains(t, data, "time_series_data")
	})

	// Test credit analytics with custom date range
	suite.T().Run("GetCreditAnalyticsCustomRange", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/credit-analytics?start_date=2024-01-01&end_date=2024-01-31&group_by=day", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})
}

// TestAICreditsPurchase tests AI credits purchase functionality
func (suite *IntegrationTestSuite) TestAICreditsPurchase() {
	// Test getting AI credits pricing
	suite.T().Run("GetAICreditsPricing", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("GET", "/api/ai-credits/pricing", nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "currency")
		assert.Contains(t, data, "tiers")
		assert.Contains(t, data, "base_rate")
	})

	// Test pricing calculation
	suite.T().Run("GetPricingCalculation", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("GET", "/api/ai-credits/pricing/calculate?amount=1000", nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "amount")
		assert.Contains(t, data, "total_cost")
		assert.Contains(t, data, "tier")
	})

	// Test purchasing AI credits
	suite.T().Run("PurchaseAICredits", func(t *testing.T) {
		purchaseData := map[string]interface{}{
			"amount":       1000,
			"payment_type": "stripe",
		}

		url := fmt.Sprintf("/api/organizations/%s/ai-credits/purchase", suite.orgID)
		w := suite.makeAuthenticatedRequest("POST", url, purchaseData)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "transaction_id")
		assert.Contains(t, data, "amount")
		assert.Contains(t, data, "total_cost")
		assert.Contains(t, data, "new_balance")
	})

	// Test purchasing with invalid amount
	suite.T().Run("PurchaseAICreditsInvalidAmount", func(t *testing.T) {
		purchaseData := map[string]interface{}{
			"amount":       50, // Below minimum
			"payment_type": "stripe",
		}

		url := fmt.Sprintf("/api/organizations/%s/ai-credits/purchase", suite.orgID)
		w := suite.makeAuthenticatedRequest("POST", url, purchaseData)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})

	// Test purchasing with invalid payment type
	suite.T().Run("PurchaseAICreditsInvalidPaymentType", func(t *testing.T) {
		purchaseData := map[string]interface{}{
			"amount":       1000,
			"payment_type": "invalid_payment",
		}

		url := fmt.Sprintf("/api/organizations/%s/ai-credits/purchase", suite.orgID)
		w := suite.makeAuthenticatedRequest("POST", url, purchaseData)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})
}

// TestAICreditsEndpoints tests existing AI credits endpoints
func (suite *IntegrationTestSuite) TestAICreditsEndpoints() {
	// Test getting AI credits
	suite.T().Run("GetAICredits", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/ai-credits", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "remaining_credits")
		assert.Contains(t, data, "organization_id")
	})

	// Test getting AI credits history
	suite.T().Run("GetAICreditsHistory", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/ai-credits/history", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})

	// Test getting AI credits usage
	suite.T().Run("GetAICreditsUsage", func(t *testing.T) {
		url := fmt.Sprintf("/api/organizations/%s/ai-credits/usage", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})
}
