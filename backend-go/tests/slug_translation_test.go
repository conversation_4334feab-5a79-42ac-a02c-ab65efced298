package tests

import (
	"encoding/json"
	"fmt"
	"net/http"

	"adc-multi-languages/models"
	"github.com/stretchr/testify/assert"
)

// TestSlugLookups tests slug-based lookups for organizations and projects
func (suite *IntegrationTestSuite) TestSlugLookups() {
	// Test getting organization by slug
	suite.T().Run("GetOrganizationBySlug", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("GET", "/api/organizations/slug/test-org", nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "Test Organization", data["name"])
		assert.Equal(t, "test-org", data["slug"])
		assert.Contains(t, data, "member_count")
		assert.Contains(t, data, "project_count")
		assert.Contains(t, data, "user_role")
	})

	// Test getting project by slug
	suite.T().Run("GetProjectBySlug", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("GET", "/api/projects/slug/test-project", nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "Test Project", data["name"])
		assert.Equal(t, "test-project", data["slug"])
		assert.Contains(t, data, "organization")
		assert.Contains(t, data, "locale_count")
		assert.Contains(t, data, "key_count")
	})

	// Test organization slug availability (existing slug)
	suite.T().Run("CheckOrganizationSlugAvailabilityExisting", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("GET", "/api/organizations/slug/test-org/available", nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "test-org", data["slug"])
		assert.Equal(t, false, data["available"])
	})

	// Test organization slug availability (new slug)
	suite.T().Run("CheckOrganizationSlugAvailabilityNew", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("GET", "/api/organizations/slug/new-unique-org/available", nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "new-unique-org", data["slug"])
		assert.Equal(t, true, data["available"])
	})

	// Test project slug availability
	suite.T().Run("CheckProjectSlugAvailability", func(t *testing.T) {
		url := fmt.Sprintf("/api/projects/slug/new-project/available?organization_id=%s", suite.orgID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "new-project", data["slug"])
		assert.Equal(t, true, data["available"])
		assert.Equal(t, "organization", data["scope"])
	})

	// Test getting non-existent organization by slug
	suite.T().Run("GetNonExistentOrganizationBySlug", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("GET", "/api/organizations/slug/non-existent-org", nil)

		assert.Equal(t, http.StatusNotFound, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})

	// Test getting non-existent project by slug
	suite.T().Run("GetNonExistentProjectBySlug", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("GET", "/api/projects/slug/non-existent-project", nil)

		assert.Equal(t, http.StatusNotFound, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})
}

// TestTranslationManagement tests translation CRUD operations
func (suite *IntegrationTestSuite) TestTranslationManagement() {
	var translationKeyID string
	var translationID string

	// First create a translation key
	suite.T().Run("CreateTranslationKey", func(t *testing.T) {
		keyData := map[string]interface{}{
			"project_id":  suite.projectID,
			"key":         "test.welcome.message",
			"description": "Welcome message for testing",
		}

		w := suite.makeAuthenticatedRequest("POST", "/api/translations/keys", keyData)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		translationKeyID = data["id"].(string)
		assert.Equal(t, "test.welcome.message", data["key"])
	})

	// Get a locale ID for creating translations
	var localeID string
	var locale models.Locale
	err := suite.db.Where("code = ?", "en").First(&locale).Error
	suite.Require().NoError(err)
	localeID = locale.ID.String()

	// Create a translation
	suite.T().Run("CreateTranslation", func(t *testing.T) {
		translationData := map[string]interface{}{
			"key_id":    translationKeyID,
			"locale_id": localeID,
			"content":   "Welcome to our application!",
		}

		w := suite.makeAuthenticatedRequest("POST", "/api/translations", translationData)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		translationID = data["id"].(string)
		assert.Equal(t, "Welcome to our application!", data["content"])
	})

	// Test listing translations with filters
	suite.T().Run("ListTranslations", func(t *testing.T) {
		url := fmt.Sprintf("/api/translations?key_id=%s", translationKeyID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].([]interface{})
		assert.Len(t, data, 1)

		translation := data[0].(map[string]interface{})
		assert.Equal(t, translationID, translation["id"])
	})

	// Test getting specific translation
	suite.T().Run("GetTranslation", func(t *testing.T) {
		url := fmt.Sprintf("/api/translations/%s", translationID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, translationID, data["id"])
		assert.Equal(t, "Welcome to our application!", data["content"])
	})

	// Test updating translation
	suite.T().Run("UpdateTranslation", func(t *testing.T) {
		updateData := map[string]interface{}{
			"content":     "Welcome to our amazing application!",
			"is_fuzzy":    false,
			"is_reviewed": true,
		}

		url := fmt.Sprintf("/api/translations/%s", translationID)
		w := suite.makeAuthenticatedRequest("PUT", url, updateData)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "Welcome to our amazing application!", data["content"])
		assert.Equal(t, false, data["is_fuzzy"])
		assert.Equal(t, true, data["is_reviewed"])
	})

	// Test getting translation history
	suite.T().Run("GetTranslationHistory", func(t *testing.T) {
		url := fmt.Sprintf("/api/translations/%s/history", translationID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])

		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 1) // Should have at least one history entry
	})

	// Test listing translations with different filters
	suite.T().Run("ListTranslationsByLocale", func(t *testing.T) {
		url := fmt.Sprintf("/api/translations?locale_id=%s", localeID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})

	// Test listing translations by project
	suite.T().Run("ListTranslationsByProject", func(t *testing.T) {
		url := fmt.Sprintf("/api/translations?project_id=%s", suite.projectID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, true, response["success"])
	})

	// Test listing translations without filters (should fail)
	suite.T().Run("ListTranslationsNoFilters", func(t *testing.T) {
		w := suite.makeAuthenticatedRequest("GET", "/api/translations", nil)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})

	// Test getting non-existent translation
	suite.T().Run("GetNonExistentTranslation", func(t *testing.T) {
		fakeID := "00000000-0000-0000-0000-000000000000"
		url := fmt.Sprintf("/api/translations/%s", fakeID)
		w := suite.makeAuthenticatedRequest("GET", url, nil)

		assert.Equal(t, http.StatusNotFound, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, false, response["success"])
	})
}
