# ADC Multi-Languages Backend (Go)

A comprehensive translation management platform backend built with Go, Gin, and GORM. This implementation provides a complete API for managing translations, users, organizations, and billing.

## 🚀 Features

### Core Functionality
- **User Management**: Registration, authentication, profile management
- **Organization Management**: Multi-tenant organization support
- **Project Management**: Translation project organization
- **Translation System**: Key-value translation management
- **Locale Management**: Language and locale configuration
- **API Key Management**: Secure API access with permissions
- **Permission Groups**: Flexible role-based access control
- **AI Credits System**: Usage tracking and billing
- **Subscription Management**: Plan management and billing
- **Email Services**: Password reset and verification emails
- **Rate Limiting**: API protection and abuse prevention
- **Caching**: Performance optimization
- **Webhooks**: Stripe integration for payments

### Technical Features
- **JWT Authentication**: Secure token-based authentication
- **Database Migrations**: Automatic schema management
- **Structured Logging**: Comprehensive request tracking
- **Error Handling**: Standardized error responses
- **Input Validation**: Request validation with detailed errors
- **Pagination**: Efficient data retrieval
- **CORS Support**: Cross-origin resource sharing
- **Health Checks**: Service monitoring endpoints

## 🛠 Technology Stack

- **Language**: Go 1.21+
- **Web Framework**: Gin
- **ORM**: GORM
- **Database**: PostgreSQL
- **Authentication**: JWT tokens
- **Email**: SMTP
- **Caching**: In-memory cache
- **Rate Limiting**: Token bucket algorithm

## 📋 Prerequisites

- Go 1.21 or higher
- PostgreSQL 12 or higher
- SMTP server (for email functionality)

## 🔧 Installation & Setup

### Installation

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd adc-multi-languages/backend-go
   ```

2. **Install dependencies**:
   ```bash
   go mod download
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**:
   ```bash
   # Create database
   createdb adc_multi_languages
   
   # The application will automatically run migrations on startup
   ```

5. **Run the application**:
   ```bash
   go run main.go
   ```

The server will start on `http://localhost:8300` by default.

### Development

For development with auto-reload, you can use `air`:

```bash
# Install air
go install github.com/cosmtrek/air@latest

# Run with auto-reload
air
```

## API Documentation

### Response Format

All API responses follow this standardized format:

```json
{
  "success": true,
  "status": 200,
  "message": "Optional message",
  "data": {
    // Response data
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid",
    "version": "1.0",
    "pagination": {
      "page": 1,
      "per_page": 10,
      "total": 100,
      "total_pages": 10
    }
  }
}
```

### Pagination

Pagination is supported through query parameters:

- `page`: Page number (default: 1)
- `per_page` or `limit`: Items per page (default: 10, max: 100)
- `offset`: Alternative to page-based pagination

### Authentication

The API supports two authentication methods:

1. **JWT Tokens**: For user authentication
   ```
   Authorization: Bearer <jwt-token>
   ```

2. **API Keys**: For programmatic access
   ```
   X-API-Key: <api-key>
   ```

### Available Endpoints

#### Public Endpoints

- `GET /` - API information
- `GET /health` - Health check
- `GET /api/hello` - Hello world
- `GET /api/hello/:name` - Personalized hello
- `POST /api/echo` - Echo request body
- `GET /api/error-example` - Error examples for testing

#### Authentication Endpoints

- `POST /api/auth/signup` - User registration
- `POST /api/auth/signin` - User login
- `POST /api/auth/refresh-token` - Refresh JWT token
- `POST /api/auth/request-password-reset` - Request password reset
- `POST /api/auth/confirm-password-reset` - Confirm password reset
- `POST /api/auth/request-email-verification` - Request email verification
- `POST /api/auth/verify-email` - Verify email
- `POST /api/auth/google-auth` - Google OAuth

#### Protected Endpoints (require authentication)

- **Users**: `/api/users/*`
- **Organizations**: `/api/organizations/*`
- **Projects**: `/api/projects/*`
- **Locales**: `/api/locales/*`
- **Translations**: `/api/translations/*`
- **API Keys**: `/api/api-keys/*`
- **Admin**: `/api/admin/*`

## Configuration

The application can be configured through environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | `8300` |
| `HOST` | Server host | `0.0.0.0` |
| `ENVIRONMENT` | Environment (development/production) | `development` |
| `DATABASE_URL` | Complete database URL | - |
| `JWT_SECRET` | JWT signing secret | - |
| `LOG_LEVEL` | Logging level (debug/info/warn/error) | `info` |

See `.env.example` for a complete list of configuration options.

## Database

The application uses PostgreSQL with GORM ORM. Database migrations are automatically applied on startup.

### Models

- **User**: User accounts and profiles
- **Organization**: Organizations/teams
- **Project**: Translation projects
- **Locale**: Supported languages/locales
- **TranslationKey**: Translation keys
- **Translation**: Actual translations
- **APIKey**: API keys for programmatic access
- **APICallLog**: API usage audit logs
- **PermissionAuditLog**: Permission check audit logs

## Logging

The application uses structured JSON logging with the following features:

- Request/response logging with timing
- Error tracking with context
- API call auditing
- Permission check auditing
- Request ID tracking for correlation

## Testing

```bash
# Run tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run tests with verbose output
go test -v ./...
```

## Deployment

### Docker

```bash
# Build Docker image
docker build -t adc-multi-languages-go .

# Run container
docker run -p 8300:8300 --env-file .env adc-multi-languages-go
```

### Production Considerations

1. Set `ENVIRONMENT=production`
2. Use a strong `JWT_SECRET`
3. Configure proper database credentials
4. Set up SSL/TLS termination
5. Configure log aggregation
6. Set up monitoring and health checks

## Migration from Rust

This Go implementation maintains API compatibility with the original Rust backend:

- Same endpoint structure and naming
- Identical response format
- Compatible authentication mechanisms
- Equivalent error handling
- Same pagination approach

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

[Your License Here]
