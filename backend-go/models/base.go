package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseModel contains common fields for all models
type BaseModel struct {
	ID        uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// BeforeCreate sets the ID before creating a record
func (base *BaseModel) BeforeCreate(tx *gorm.DB) error {
	if base.ID == uuid.Nil {
		base.ID = uuid.New()
	}
	return nil
}

// User represents a user in the system
type User struct {
	BaseModel
	Email             string     `json:"email" gorm:"uniqueIndex;not null"`
	Username          *string    `json:"username" gorm:"uniqueIndex"`
	PasswordHash      string     `json:"-" gorm:"not null"`
	FirstName         *string    `json:"first_name"`
	LastName          *string    `json:"last_name"`
	EmailVerified     bool       `json:"email_verified" gorm:"default:false"`
	EmailVerifiedAt   *time.Time `json:"email_verified_at"`
	LastLoginAt       *time.Time `json:"last_login_at"`
	IsActive          bool       `json:"is_active" gorm:"default:true"`
	ProfilePictureURL *string    `json:"profile_picture_url"`
	Timezone          *string    `json:"timezone"`
	Language          *string    `json:"language" gorm:"default:'en'"`
}

// Organization represents an organization in the system
type Organization struct {
	BaseModel
	Name                      string     `json:"name" gorm:"not null"`
	Slug                      string     `json:"slug" gorm:"uniqueIndex;not null"`
	Description               *string    `json:"description"`
	Website                   *string    `json:"website"`
	LogoURL                   *string    `json:"logo_url"`
	OwnerID                   uuid.UUID  `json:"owner_id" gorm:"type:uuid;not null"`
	Owner                     User       `json:"owner" gorm:"foreignKey:OwnerID"`
	SubscriptionTier          string     `json:"subscription_tier" gorm:"default:'free'"`
	SubscriptionStatus        *string    `json:"subscription_status"`
	SubscriptionTierID        *uuid.UUID `json:"subscription_tier_id" gorm:"type:uuid"`
	SubscriptionAutoRenew     *bool      `json:"subscription_auto_renew" gorm:"default:true"`
	BillingPeriodStart        *time.Time `json:"billing_period_start"`
	BillingPeriodEnd          *time.Time `json:"billing_period_end"`
	StripeCustomerID          *string    `json:"stripe_customer_id"`
	StripeSubscriptionID      *string    `json:"stripe_subscription_id"`
	AICreditsMonthlyAllowance *int       `json:"ai_credits_monthly_allowance" gorm:"default:1000"`
	AICreditsRemaining        *int       `json:"ai_credits_remaining" gorm:"default:1000"`
	AICreditsResetDate        *time.Time `json:"ai_credits_reset_date"`
}

// OrganizationMembership represents a user's membership in an organization
type OrganizationMembership struct {
	BaseModel
	OrganizationID uuid.UUID    `json:"organization_id" gorm:"type:uuid;not null"`
	Organization   Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	UserID         uuid.UUID    `json:"user_id" gorm:"type:uuid;not null"`
	User           User         `json:"user" gorm:"foreignKey:UserID"`
	Role           string       `json:"role" gorm:"not null;default:'member'"` // owner, admin, member
	IsActive       bool         `json:"is_active" gorm:"default:true"`
	InvitedBy      *uuid.UUID   `json:"invited_by" gorm:"type:uuid"`
	Inviter        *User        `json:"inviter,omitempty" gorm:"foreignKey:InvitedBy"`
	JoinedAt       *time.Time   `json:"joined_at"`
}

// Project represents a project in the system
type Project struct {
	BaseModel
	Name           string       `json:"name" gorm:"not null"`
	Slug           string       `json:"slug" gorm:"not null"`
	Description    *string      `json:"description"`
	OrganizationID uuid.UUID    `json:"organization_id" gorm:"type:uuid;not null"`
	Organization   Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	DefaultLocale  *string      `json:"default_locale" gorm:"default:'en'"`
	IsPublic       *bool        `json:"is_public" gorm:"default:false"`
	CreatedBy      *uuid.UUID   `json:"created_by" gorm:"type:uuid"`
	Creator        *User        `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
}

// ProjectLocale represents a locale associated with a project
type ProjectLocale struct {
	BaseModel
	ProjectID uuid.UUID `json:"project_id" gorm:"type:uuid;not null"`
	Project   Project   `json:"project" gorm:"foreignKey:ProjectID"`
	LocaleID  uuid.UUID `json:"locale_id" gorm:"type:uuid;not null"`
	Locale    Locale    `json:"locale" gorm:"foreignKey:LocaleID"`
	IsActive  bool      `json:"is_active" gorm:"default:true"`
}

// Resource represents a resource in a project
type Resource struct {
	BaseModel
	ProjectID   uuid.UUID  `json:"project_id" gorm:"type:uuid;not null"`
	Project     Project    `json:"project" gorm:"foreignKey:ProjectID"`
	Name        string     `json:"name" gorm:"not null"`
	Type        string     `json:"type" gorm:"not null"` // e.g., "json", "yaml", "properties"
	Path        *string    `json:"path"`
	Description *string    `json:"description"`
	CreatedBy   *uuid.UUID `json:"created_by" gorm:"type:uuid"`
	Creator     *User      `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
}

// TranslationKey represents a translation key in the system
type TranslationKey struct {
	BaseModel
	ResourceID    *uuid.UUID `json:"resource_id" gorm:"type:uuid"`
	Resource      *Resource  `json:"resource,omitempty" gorm:"foreignKey:ResourceID"`
	ProjectID     *uuid.UUID `json:"project_id" gorm:"type:uuid"`
	Project       *Project   `json:"project,omitempty" gorm:"foreignKey:ProjectID"`
	KeyName       string     `json:"key_name" gorm:"not null"`
	Description   *string    `json:"description"`
	Context       *string    `json:"context"`
	IsPlural      *bool      `json:"is_plural" gorm:"default:false"`
	MaxLength     *int       `json:"max_length"`
	ScreenshotURL *string    `json:"screenshot_url"`
	CreatedBy     *uuid.UUID `json:"created_by" gorm:"type:uuid"`
	Creator       *User      `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
}

// Translation represents a translation for a specific key and locale
type Translation struct {
	BaseModel
	KeyID      uuid.UUID      `json:"key_id" gorm:"type:uuid;not null"`
	Key        TranslationKey `json:"key" gorm:"foreignKey:KeyID"`
	LocaleID   uuid.UUID      `json:"locale_id" gorm:"type:uuid;not null"`
	Locale     Locale         `json:"locale" gorm:"foreignKey:LocaleID"`
	Content    string         `json:"content" gorm:"not null"`
	IsFuzzy    *bool          `json:"is_fuzzy" gorm:"default:false"`
	IsReviewed *bool          `json:"is_reviewed" gorm:"default:false"`
	ReviewedBy *uuid.UUID     `json:"reviewed_by" gorm:"type:uuid"`
	Reviewer   *User          `json:"reviewer,omitempty" gorm:"foreignKey:ReviewedBy"`
	ReviewedAt *time.Time     `json:"reviewed_at"`
	CreatedBy  *uuid.UUID     `json:"created_by" gorm:"type:uuid"`
	Creator    *User          `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
}

// TranslationHistory represents the history of changes to a translation
type TranslationHistory struct {
	BaseModel
	TranslationID uuid.UUID   `json:"translation_id" gorm:"type:uuid;not null"`
	Translation   Translation `json:"translation" gorm:"foreignKey:TranslationID"`
	Content       string      `json:"content" gorm:"not null"`
	Action        string      `json:"action" gorm:"not null"` // created, updated, deleted
	PerformedBy   *uuid.UUID  `json:"performed_by" gorm:"type:uuid"`
	Performer     *User       `json:"performer,omitempty" gorm:"foreignKey:PerformedBy"`
}

// Locale represents a locale/language in the system
type Locale struct {
	BaseModel
	Code       string `json:"code" gorm:"uniqueIndex;not null"` // e.g., "en", "es", "fr"
	Name       string `json:"name" gorm:"not null"`             // e.g., "English", "Spanish", "French"
	NativeName string `json:"native_name" gorm:"not null"`      // e.g., "English", "Español", "Français"
	IsActive   bool   `json:"is_active" gorm:"default:true"`
	Direction  string `json:"direction" gorm:"default:'ltr'"` // "ltr" or "rtl"
}

// APIKey represents an API key in the system
type APIKey struct {
	BaseModel
	Name           string       `json:"name" gorm:"not null"`
	Key            string       `json:"key" gorm:"uniqueIndex;not null"`
	OrganizationID uuid.UUID    `json:"organization_id" gorm:"type:uuid;not null"`
	Organization   Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	IsActive       bool         `json:"is_active" gorm:"default:true"`
	LastUsedAt     *time.Time   `json:"last_used_at"`
	ExpiresAt      *time.Time   `json:"expires_at"`
	CreatedBy      uuid.UUID    `json:"created_by" gorm:"type:uuid;not null"`
	Creator        User         `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// APICallLog represents an API call log entry
type APICallLog struct {
	BaseModel
	APIKeyID          uuid.UUID    `json:"api_key_id" gorm:"type:uuid;not null"`
	APIKey            APIKey       `json:"api_key" gorm:"foreignKey:APIKeyID"`
	OrganizationID    uuid.UUID    `json:"organization_id" gorm:"type:uuid;not null"`
	Organization      Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	Endpoint          string       `json:"endpoint" gorm:"not null"`
	Method            string       `json:"method" gorm:"not null"`
	StatusCode        int          `json:"status_code" gorm:"not null"`
	ResponseTimeMs    int          `json:"response_time_ms" gorm:"not null"`
	RequestSizeBytes  *int         `json:"request_size_bytes"`
	ResponseSizeBytes *int         `json:"response_size_bytes"`
	IPAddress         *string      `json:"ip_address"`
	UserAgent         *string      `json:"user_agent"`
}

// PermissionAuditLog represents a permission audit log entry
type PermissionAuditLog struct {
	BaseModel
	APIKeyID       uuid.UUID    `json:"api_key_id" gorm:"type:uuid;not null"`
	APIKey         APIKey       `json:"api_key" gorm:"foreignKey:APIKeyID"`
	OrganizationID uuid.UUID    `json:"organization_id" gorm:"type:uuid;not null"`
	Organization   Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	ResourcePath   string       `json:"resource_path" gorm:"not null"`
	Method         string       `json:"method" gorm:"not null"`
	PermissionKey  string       `json:"permission_key" gorm:"not null"`
	Granted        bool         `json:"granted" gorm:"not null"`
}

// PasswordResetToken represents a password reset token
type PasswordResetToken struct {
	BaseModel
	UserID    uuid.UUID  `json:"user_id" gorm:"type:uuid;not null"`
	User      User       `json:"user" gorm:"foreignKey:UserID"`
	Token     string     `json:"token" gorm:"uniqueIndex;not null"`
	ExpiresAt time.Time  `json:"expires_at" gorm:"not null"`
	UsedAt    *time.Time `json:"used_at"`
}

// EmailVerificationToken represents an email verification token
type EmailVerificationToken struct {
	BaseModel
	UserID    uuid.UUID  `json:"user_id" gorm:"type:uuid;not null"`
	User      User       `json:"user" gorm:"foreignKey:UserID"`
	Token     string     `json:"token" gorm:"uniqueIndex;not null"`
	Email     string     `json:"email" gorm:"not null"`
	ExpiresAt time.Time  `json:"expires_at" gorm:"not null"`
	UsedAt    *time.Time `json:"used_at"`
}

// PermissionGroup represents a permission group in the system
type PermissionGroup struct {
	BaseModel
	Name           string       `json:"name" gorm:"not null"`
	Description    string       `json:"description"`
	OrganizationID uuid.UUID    `json:"organization_id" gorm:"type:uuid;not null"`
	Organization   Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	Permissions    string       `json:"permissions" gorm:"type:text;not null"` // JSON string of permissions
	CreatedBy      uuid.UUID    `json:"created_by" gorm:"type:uuid;not null"`
	Creator        User         `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// AICreditTransaction represents an AI credit transaction
type AICreditTransaction struct {
	BaseModel
	OrganizationID  uuid.UUID    `json:"organization_id" gorm:"type:uuid;not null"`
	Organization    Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	TransactionType string       `json:"transaction_type" gorm:"not null"` // purchase, usage, refund, adjustment
	Amount          int          `json:"amount" gorm:"not null"`           // positive for credits added, negative for credits used
	BalanceBefore   int          `json:"balance_before" gorm:"not null"`
	BalanceAfter    int          `json:"balance_after" gorm:"not null"`
	Description     string       `json:"description"`
	Reference       *string      `json:"reference"` // external reference (e.g., Stripe payment ID)
	PerformedBy     *uuid.UUID   `json:"performed_by" gorm:"type:uuid"`
	Performer       *User        `json:"performer,omitempty" gorm:"foreignKey:PerformedBy"`
}

// AICreditUsage represents AI credit usage tracking
type AICreditUsage struct {
	BaseModel
	OrganizationID   uuid.UUID    `json:"organization_id" gorm:"type:uuid;not null"`
	Organization     Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	UserID           *uuid.UUID   `json:"user_id" gorm:"type:uuid"`
	User             *User        `json:"user,omitempty" gorm:"foreignKey:UserID"`
	ProjectID        *uuid.UUID   `json:"project_id" gorm:"type:uuid"`
	Project          *Project     `json:"project,omitempty" gorm:"foreignKey:ProjectID"`
	CreditsUsed      int          `json:"credits_used" gorm:"not null"`
	Operation        string       `json:"operation" gorm:"not null"` // translate, review, etc.
	SourceLocale     *string      `json:"source_locale"`
	TargetLocale     *string      `json:"target_locale"`
	TextLength       *int         `json:"text_length"`
	ModelUsed        *string      `json:"model_used"`
	APIKeyID         *uuid.UUID   `json:"api_key_id" gorm:"type:uuid"`
	APIKey           *APIKey      `json:"api_key,omitempty" gorm:"foreignKey:APIKeyID"`
	RemainingCredits int          `json:"remaining_credits" gorm:"not null"`
}

// SubscriptionPlan represents a subscription plan
type SubscriptionPlan struct {
	BaseModel
	Name                      string   `json:"name" gorm:"not null"`
	Description               string   `json:"description"`
	PriceMonthly              float64  `json:"price_monthly" gorm:"not null"`
	PriceYearly               *float64 `json:"price_yearly"`
	AICreditsMonthlyAllowance int      `json:"ai_credits_monthly_allowance" gorm:"not null"`
	MaxProjects               *int     `json:"max_projects"`
	MaxUsers                  *int     `json:"max_users"`
	Features                  string   `json:"features" gorm:"type:text"` // JSON string of features
	IsActive                  bool     `json:"is_active" gorm:"default:true"`
	StripePriceIDMonthly      *string  `json:"stripe_price_id_monthly"`
	StripePriceIDYearly       *string  `json:"stripe_price_id_yearly"`
}

// OrganizationSubscription represents an organization's subscription
type OrganizationSubscription struct {
	BaseModel
	OrganizationID       uuid.UUID        `json:"organization_id" gorm:"type:uuid;not null"`
	Organization         Organization     `json:"organization" gorm:"foreignKey:OrganizationID"`
	SubscriptionPlanID   uuid.UUID        `json:"subscription_plan_id" gorm:"type:uuid;not null"`
	SubscriptionPlan     SubscriptionPlan `json:"subscription_plan" gorm:"foreignKey:SubscriptionPlanID"`
	Status               string           `json:"status" gorm:"not null"`         // active, canceled, past_due, etc.
	BillingPeriod        string           `json:"billing_period" gorm:"not null"` // monthly, yearly
	CurrentPeriodStart   time.Time        `json:"current_period_start" gorm:"not null"`
	CurrentPeriodEnd     time.Time        `json:"current_period_end" gorm:"not null"`
	CancelAtPeriodEnd    bool             `json:"cancel_at_period_end" gorm:"default:false"`
	StripeSubscriptionID *string          `json:"stripe_subscription_id"`
	TrialStart           *time.Time       `json:"trial_start"`
	TrialEnd             *time.Time       `json:"trial_end"`
}

// CreditLimit represents credit limit settings for an organization
type CreditLimit struct {
	BaseModel
	OrganizationID      uuid.UUID    `json:"organization_id" gorm:"type:uuid;not null;unique;index"`
	Organization        Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	MonthlyLimit        *int         `json:"monthly_limit"`     // null means unlimited
	WarningThreshold    *int         `json:"warning_threshold"` // percentage (0-100)
	AutoPurchaseEnabled bool         `json:"auto_purchase_enabled" gorm:"default:false"`
	AutoPurchaseAmount  *int         `json:"auto_purchase_amount"`
}

// AuditLog represents a general audit log entry
type AuditLog struct {
	BaseModel
	UserID       uuid.UUID  `json:"user_id" gorm:"type:uuid;not null"`
	User         User       `json:"user" gorm:"foreignKey:UserID"`
	Action       string     `json:"action" gorm:"not null"`
	ResourceType string     `json:"resource_type" gorm:"not null"`
	ResourceID   *uuid.UUID `json:"resource_id" gorm:"type:uuid"`
	Details      string     `json:"details"`
	IPAddress    *string    `json:"ip_address"`
	UserAgent    *string    `json:"user_agent"`
	Success      bool       `json:"success" gorm:"default:true"`
	ErrorMessage *string    `json:"error_message"`
}
