# ADC Multi-Languages Backend - Deployment Guide

This guide covers various deployment options for the ADC Multi-Languages backend.

## 🚀 Quick Start (Development)

### Prerequisites
- Go 1.21+
- PostgreSQL 12+
- Git

### Local Development Setup

1. **Clone and setup**:
   ```bash
   cd backend-go
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Install dependencies**:
   ```bash
   go mod download
   ```

3. **Setup database**:
   ```bash
   createdb adc_multi_languages
   ```

4. **Run the application**:
   ```bash
   go run .
   ```

The server will start on `http://localhost:8080`

## 🐳 Docker Deployment

### Single Container

1. **Build the image**:
   ```bash
   docker build -t adc-backend .
   ```

2. **Run with environment file**:
   ```bash
   docker run -p 8080:8080 --env-file .env adc-backend
   ```

### Docker Compose (Recommended)

1. **Start all services**:
   ```bash
   docker-compose up -d
   ```

2. **View logs**:
   ```bash
   docker-compose logs -f
   ```

3. **Stop services**:
   ```bash
   docker-compose down
   ```

Services included:
- **Backend**: ADC API server (port 8080)
- **PostgreSQL**: Database (port 5432)
- **Redis**: Caching (port 6379)
- **Nginx**: Reverse proxy (port 80/443)

## ☁️ Cloud Deployment

### AWS ECS

1. **Build and push to ECR**:
   ```bash
   aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account>.dkr.ecr.us-east-1.amazonaws.com
   docker build -t adc-backend .
   docker tag adc-backend:latest <account>.dkr.ecr.us-east-1.amazonaws.com/adc-backend:latest
   docker push <account>.dkr.ecr.us-east-1.amazonaws.com/adc-backend:latest
   ```

2. **Create ECS task definition** with environment variables
3. **Deploy to ECS service**

### Google Cloud Run

1. **Build and deploy**:
   ```bash
   gcloud builds submit --tag gcr.io/PROJECT-ID/adc-backend
   gcloud run deploy --image gcr.io/PROJECT-ID/adc-backend --platform managed
   ```

### Heroku

1. **Create Heroku app**:
   ```bash
   heroku create adc-backend
   ```

2. **Set environment variables**:
   ```bash
   heroku config:set JWT_SECRET=your-secret
   heroku config:set DB_HOST=your-db-host
   # ... other variables
   ```

3. **Deploy**:
   ```bash
   git push heroku main
   ```

## 🔧 Production Configuration

### Environment Variables

**Required**:
```env
# Database
DB_HOST=your-db-host
DB_USER=your-db-user
DB_PASSWORD=your-db-password
DB_NAME=adc_multi_languages

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key

# Email
SMTP_HOST=smtp.your-provider.com
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
```

**Optional but Recommended**:
```env
# AI Translation
OPENAI_API_KEY=your-openai-key
GOOGLE_TRANSLATE_API_KEY=your-google-key
AZURE_TRANSLATOR_API_KEY=your-azure-key

# Stripe
STRIPE_SECRET_KEY=your-stripe-secret
STRIPE_WEBHOOK_SECRET=your-webhook-secret

# Redis (for external caching)
REDIS_HOST=your-redis-host
REDIS_PASSWORD=your-redis-password
```

### Security Checklist

- [ ] Use strong, unique JWT secrets
- [ ] Enable HTTPS in production
- [ ] Configure proper CORS origins
- [ ] Set up rate limiting
- [ ] Use environment-specific database credentials
- [ ] Enable database SSL in production
- [ ] Configure proper logging levels
- [ ] Set up monitoring and alerting
- [ ] Use secrets management (AWS Secrets Manager, etc.)
- [ ] Enable database backups

### Performance Optimization

1. **Database**:
   - Use connection pooling
   - Enable query optimization
   - Set up read replicas if needed
   - Configure proper indexes

2. **Caching**:
   - Use Redis for external caching
   - Configure appropriate TTL values
   - Enable cache warming

3. **Load Balancing**:
   - Use multiple backend instances
   - Configure health checks
   - Set up auto-scaling

## 📊 Monitoring & Observability

### Health Checks

- **Application**: `GET /health`
- **Database**: `GET /api/test/database`
- **Metrics**: `GET /metrics` (Prometheus format)

### Logging

The application uses structured JSON logging:
```json
{
  "level": "info",
  "msg": "Request completed",
  "method": "GET",
  "path": "/api/users/me",
  "status": 200,
  "duration": "45ms",
  "user_id": "uuid",
  "request_id": "uuid"
}
```

### Metrics

Available at `/metrics` in Prometheus format:
- Request count and duration
- Error rates
- Memory usage
- Active users
- Translation usage
- Database connections

### Alerting

Set up alerts for:
- High error rates (>5%)
- Slow response times (>2s)
- High memory usage (>80%)
- Database connection issues
- Failed background jobs

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Go
        uses: actions/setup-go@v2
        with:
          go-version: 1.21
          
      - name: Run tests
        run: go test ./...
        
      - name: Build Docker image
        run: docker build -t adc-backend .
        
      - name: Deploy to production
        run: |
          # Your deployment commands here
```

## 🗄️ Database Management

### Migrations

Migrations run automatically on startup. For manual control:

```bash
# Run migrations
go run . --migrate

# Check migration status
psql -d adc_multi_languages -c "SELECT * FROM schema_migrations;"
```

### Backups

Set up automated backups:

```bash
# Daily backup script
pg_dump adc_multi_languages > backup_$(date +%Y%m%d).sql

# Restore from backup
psql adc_multi_languages < backup_20240101.sql
```

### Performance Indexes

Run after initial migration:
```sql
SELECT create_performance_indexes();
```

## 🔧 Troubleshooting

### Common Issues

1. **Database connection failed**:
   - Check database credentials
   - Verify database is running
   - Check network connectivity

2. **Email not sending**:
   - Verify SMTP configuration
   - Check email credentials
   - Test with a simple email client

3. **High memory usage**:
   - Check for memory leaks
   - Adjust cache settings
   - Monitor goroutine count

4. **Slow API responses**:
   - Check database query performance
   - Review cache hit rates
   - Monitor background job queue

### Debug Mode

Enable debug logging:
```env
LOG_LEVEL=debug
GIN_MODE=debug
```

### Performance Profiling

Enable profiling endpoints:
```env
ENABLE_PROFILING=true
```

Access at:
- `GET /debug/pprof/`
- `GET /debug/pprof/heap`
- `GET /debug/pprof/goroutine`

## 📞 Support

For deployment issues:
1. Check the logs for error messages
2. Verify all environment variables are set
3. Test database connectivity
4. Check service health endpoints
5. Review monitoring dashboards

For additional support, contact: <EMAIL>
