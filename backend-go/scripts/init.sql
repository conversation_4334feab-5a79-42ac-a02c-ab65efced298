-- ADC Multi-Languages Database Initialization Script
-- This script sets up the initial database configuration

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create indexes for better performance (<PERSON><PERSON>M will create tables)
-- These will be applied after <PERSON>RM creates the tables

-- Function to create indexes after tables exist
CREATE OR REPLACE FUNCTION create_performance_indexes()
RETURNS void AS $$
BEGIN
    -- Users table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_verified ON users(email_verified);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_is_active ON users(is_active);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at ON users(created_at);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_last_login_at ON users(last_login_at);
    END IF;

    -- Organizations table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'organizations') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_organizations_slug ON organizations(slug);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_organizations_owner_id ON organizations(owner_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_organizations_subscription_tier ON organizations(subscription_tier);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_organizations_stripe_customer_id ON organizations(stripe_customer_id);
    END IF;

    -- Organization memberships table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'organization_memberships') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_org_memberships_org_user ON organization_memberships(organization_id, user_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_org_memberships_user_active ON organization_memberships(user_id, is_active);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_org_memberships_role ON organization_memberships(role);
    END IF;

    -- Projects table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_organization_id ON projects(organization_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_slug ON projects(slug);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_created_by ON projects(created_by);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_is_active ON projects(is_active);
    END IF;

    -- Locales table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'locales') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_locales_code ON locales(code);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_locales_is_active ON locales(is_active);
    END IF;

    -- Translation keys table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'translation_keys') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translation_keys_project_id ON translation_keys(project_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translation_keys_key_name ON translation_keys(key_name);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translation_keys_project_key ON translation_keys(project_id, key_name);
    END IF;

    -- Translations table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'translations') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translations_key_locale ON translations(translation_key_id, locale_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translations_locale_id ON translations(locale_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translations_updated_at ON translations(updated_at);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_translations_created_by ON translations(created_by);
    END IF;

    -- API keys table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'api_keys') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_keys_organization_id ON api_keys(organization_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_keys_is_active ON api_keys(is_active);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_keys_expires_at ON api_keys(expires_at);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_keys_last_used_at ON api_keys(last_used_at);
    END IF;

    -- AI credit transactions table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'ai_credit_transactions') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ai_credit_transactions_org_id ON ai_credit_transactions(organization_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ai_credit_transactions_type ON ai_credit_transactions(transaction_type);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ai_credit_transactions_created_at ON ai_credit_transactions(created_at);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ai_credit_transactions_performed_by ON ai_credit_transactions(performed_by);
    END IF;

    -- AI credit usage table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'ai_credit_usages') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ai_credit_usage_org_id ON ai_credit_usages(organization_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ai_credit_usage_user_id ON ai_credit_usages(user_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ai_credit_usage_project_id ON ai_credit_usages(project_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ai_credit_usage_operation ON ai_credit_usages(operation);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ai_credit_usage_created_at ON ai_credit_usages(created_at);
    END IF;

    -- Permission groups table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'permission_groups') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permission_groups_org_id ON permission_groups(organization_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permission_groups_created_by ON permission_groups(created_by);
    END IF;

    -- Subscription plans table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscription_plans') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_plans_is_active ON subscription_plans(is_active);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_plans_price_monthly ON subscription_plans(price_monthly);
    END IF;

    -- Organization subscriptions table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'organization_subscriptions') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_org_subscriptions_org_id ON organization_subscriptions(organization_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_org_subscriptions_plan_id ON organization_subscriptions(subscription_plan_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_org_subscriptions_status ON organization_subscriptions(status);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_org_subscriptions_stripe_id ON organization_subscriptions(stripe_subscription_id);
    END IF;

    -- Permission audit logs table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'permission_audit_logs') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permission_audit_logs_user_id ON permission_audit_logs(user_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permission_audit_logs_resource_type ON permission_audit_logs(resource_type);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permission_audit_logs_action ON permission_audit_logs(action);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permission_audit_logs_created_at ON permission_audit_logs(created_at);
    END IF;

    -- API call logs table indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'api_call_logs') THEN
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_call_logs_api_key_id ON api_call_logs(api_key_id);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_call_logs_endpoint ON api_call_logs(endpoint);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_call_logs_status_code ON api_call_logs(status_code);
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_api_call_logs_created_at ON api_call_logs(created_at);
    END IF;

    RAISE NOTICE 'Performance indexes created successfully';
END;
$$ LANGUAGE plpgsql;

-- Insert default subscription plans
INSERT INTO subscription_plans (id, name, description, price_monthly, price_yearly, ai_credits_monthly_allowance, max_projects, max_users, features, is_active, created_at, updated_at)
VALUES 
    (uuid_generate_v4(), 'Free', 'Basic plan for individuals', 0.0, NULL, 1000, 3, 1, '["basic_translation", "3_projects", "1000_ai_credits"]', true, NOW(), NOW()),
    (uuid_generate_v4(), 'Pro', 'Professional plan for small teams', 29.99, 299.99, 10000, 25, 5, '["advanced_translation", "25_projects", "10000_ai_credits", "team_collaboration"]', true, NOW(), NOW()),
    (uuid_generate_v4(), 'Enterprise', 'Enterprise plan for large organizations', 99.99, 999.99, 50000, NULL, NULL, '["unlimited_projects", "unlimited_users", "50000_ai_credits", "priority_support", "custom_integrations"]', true, NOW(), NOW())
ON CONFLICT DO NOTHING;

-- Insert default locales
INSERT INTO locales (id, code, name, native_name, direction, is_active, created_at, updated_at)
VALUES 
    (uuid_generate_v4(), 'en', 'English', 'English', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'es', 'Spanish', 'Español', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'fr', 'French', 'Français', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'de', 'German', 'Deutsch', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'it', 'Italian', 'Italiano', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'pt', 'Portuguese', 'Português', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'ru', 'Russian', 'Русский', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'ja', 'Japanese', '日本語', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'ko', 'Korean', '한국어', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'zh', 'Chinese', '中文', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'ar', 'Arabic', 'العربية', 'rtl', true, NOW(), NOW()),
    (uuid_generate_v4(), 'hi', 'Hindi', 'हिन्दी', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'tr', 'Turkish', 'Türkçe', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'pl', 'Polish', 'Polski', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'nl', 'Dutch', 'Nederlands', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'sv', 'Swedish', 'Svenska', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'da', 'Danish', 'Dansk', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'no', 'Norwegian', 'Norsk', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'fi', 'Finnish', 'Suomi', 'ltr', true, NOW(), NOW()),
    (uuid_generate_v4(), 'cs', 'Czech', 'Čeština', 'ltr', true, NOW(), NOW())
ON CONFLICT (code) DO NOTHING;

-- Create a function to be called after GORM creates tables
-- This will be executed manually or via a trigger
COMMENT ON FUNCTION create_performance_indexes() IS 'Creates performance indexes after GORM table creation';

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'ADC Multi-Languages database initialized successfully';
    RAISE NOTICE 'Default subscription plans and locales have been inserted';
    RAISE NOTICE 'Run SELECT create_performance_indexes(); after GORM creates tables for optimal performance';
END $$;
