#!/bin/bash

# API Integration Test Runner for Go Backend
echo "🚀 Starting API Integration Tests for Go Backend"
echo "================================================"

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go first."
    exit 1
fi

# Check if PostgreSQL is running (optional - tests will show if DB is accessible)
echo "📋 Checking prerequisites..."

# Set test environment variables
export APP_ENV=test
export DATABASE_URL=${DATABASE_URL:-"postgres://postgres:password@localhost:5432/adc_test?sslmode=disable"}
export JWT_SECRET="test-secret-key-for-testing-only"
export JWT_REFRESH_SECRET="test-refresh-secret-key"

echo "🔧 Environment configured for testing"
echo "   - APP_ENV: $APP_ENV"
echo "   - DATABASE_URL: $DATABASE_URL"

# Build the application first
echo "🔨 Building application..."
if ! go build -o adc-backend .; then
    echo "❌ Build failed. Please fix compilation errors first."
    exit 1
fi

echo "✅ Build successful"

# Check if server is already running
echo "🔍 Checking if server is running..."
if curl -s http://localhost:8080/health > /dev/null 2>&1; then
    echo "✅ Server is already running"
    SERVER_RUNNING=true
else
    echo "🚀 Starting server in background..."
    ./adc-backend &
    SERVER_PID=$!
    SERVER_RUNNING=false

    # Wait for server to start
    echo "⏳ Waiting for server to start..."
    for i in {1..30}; do
        if curl -s http://localhost:8080/health > /dev/null 2>&1; then
            echo "✅ Server started successfully"
            break
        fi
        sleep 1
        if [ $i -eq 30 ]; then
            echo "❌ Server failed to start within 30 seconds"
            kill $SERVER_PID 2>/dev/null
            exit 1
        fi
    done
fi

# Run the test runner
echo "🧪 Running API integration tests..."
echo ""

go run cmd/api_tester.go test

# Stop server if we started it
if [ "$SERVER_RUNNING" = false ] && [ ! -z "$SERVER_PID" ]; then
    echo "🛑 Stopping test server..."
    kill $SERVER_PID 2>/dev/null
    wait $SERVER_PID 2>/dev/null
fi

echo ""
echo "📊 Test execution completed"

# Cleanup
rm -f adc-backend

echo "🧹 Cleanup completed"
echo "================================================"
