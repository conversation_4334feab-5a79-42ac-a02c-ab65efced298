# ADC Multi-Languages Monorepo Makefile
# This Makefile provides commands to run services in the monorepo

.PHONY: all frontend backend backend-rust dev setup clean help test test-simple build logs status install

# Default target
all: help

# Run both frontend and backend servers in parallel
dev:
	@echo "Starting both frontend and Go backend servers..."
	@make -j 2 frontend backend

# Run frontend server
frontend:
	@echo "Starting frontend server..."
	@cd frontend && bun dev --port 3300

# Run Go backend server
backend:
	@echo "Starting Go backend server..."
	@cd backend-go && go run main.go

# Run Rust backend server (legacy)
backend-rust:
	@echo "Starting Rust backend server..."
	@cd backend && cargo watch -x run

# Setup both frontend and backend
setup: setup-frontend setup-backend

# Setup frontend
setup-frontend:
	@echo "Setting up frontend..."
	@cd frontend && bun install

# Setup Go backend
setup-backend:
	@echo "Setting up Go backend..."
	@cd backend-go && go mod tidy
	@if [ ! -f backend-go/.env ]; then \
		cp backend-go/.env.example backend-go/.env; \
		echo "Created .env file from .env.example. Please update with your configuration."; \
	fi

# Setup Rust backend (legacy)
setup-backend-rust:
	@echo "Setting up Rust backend..."
	@cd backend && cargo build
	@if [ ! -f backend/.env ]; then \
		cp backend/.env.example backend/.env; \
		echo "Created .env file from .env.example. Please update with your configuration."; \
	fi

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	@cd frontend && rm -rf .next
	@cd backend-go && go clean -cache -modcache -testcache
	@cd backend-go && rm -f adc-backend

# Clean Rust backend artifacts (legacy)
clean-rust:
	@echo "Cleaning Rust backend artifacts..."
	@cd backend && cargo clean

# Test Go backend
test:
	@echo "Running Go backend tests..."
	@cd backend-go && ./run_tests.sh

# Test Go backend (simple)
test-simple:
	@echo "Running simple Go backend tests..."
	@cd backend-go && go run cmd/api_tester.go test

# Build Go backend
build:
	@echo "Building Go backend..."
	@cd backend-go && go build -o adc-backend .

# Install dependencies
install: setup

# Check status of services
status:
	@echo "Checking service status..."
	@echo "Frontend (port 3300):"
	@lsof -i :3300 || echo "  Not running"
	@echo "Backend (port 8300):"
	@lsof -i :8300 || echo "  Not running"

# View logs (requires services to be running)
logs:
	@echo "Use 'make logs-frontend' or 'make logs-backend' to view specific logs"

# Kill all running services
kill:
	@echo "Stopping all services..."
	@pkill -f "bun dev" || true
	@pkill -f "go run main.go" || true
	@pkill -f "adc-backend" || true
	@echo "All services stopped"

# Development with auto-restart (requires air for Go)
dev-watch:
	@echo "Starting development with auto-restart..."
	@echo "Note: Install 'air' for Go auto-restart: go install github.com/cosmtrek/air@latest"
	@make -j 2 frontend backend-watch

# Run Go backend with auto-restart
backend-watch:
	@echo "Starting Go backend with auto-restart..."
	@cd backend-go && air || go run main.go

# Display help information
help:
	@echo "ADC Multi-Languages Monorepo Makefile"
	@echo ""
	@echo "🚀 Main Commands:"
	@echo "  make dev              - Run both frontend and Go backend servers"
	@echo "  make dev-watch        - Run with auto-restart (requires air)"
	@echo "  make frontend         - Run only the frontend server"
	@echo "  make backend          - Run only the Go backend server"
	@echo "  make backend-watch    - Run Go backend with auto-restart"
	@echo "  make setup            - Setup both frontend and Go backend"
	@echo ""
	@echo "🔧 Setup Commands:"
	@echo "  make install          - Install all dependencies (alias for setup)"
	@echo "  make setup-frontend   - Setup only the frontend"
	@echo "  make setup-backend    - Setup only the Go backend"
	@echo "  make setup-backend-rust - Setup Rust backend (legacy)"
	@echo ""
	@echo "🧪 Testing Commands:"
	@echo "  make test             - Run comprehensive Go backend tests"
	@echo "  make test-simple      - Run simple Go backend API tests"
	@echo ""
	@echo "🏗️  Build Commands:"
	@echo "  make build            - Build Go backend binary"
	@echo "  make clean            - Clean Go backend build artifacts"
	@echo "  make clean-rust       - Clean Rust backend artifacts"
	@echo ""
	@echo "🔍 Monitoring Commands:"
	@echo "  make status           - Check if services are running"
	@echo "  make logs             - View service logs"
	@echo "  make kill             - Stop all running services"
	@echo ""
	@echo "🆘 Legacy Commands:"
	@echo "  make backend-rust     - Run Rust backend server (legacy)"
	@echo "  make help             - Display this help message"
	@echo ""
	@echo "📋 Prerequisites:"
	@echo "  - Go 1.23+ for backend"
	@echo "  - Bun for frontend"
	@echo "  - PostgreSQL for database"
	@echo ""
	@echo "🔗 Quick Start:"
	@echo "  1. make setup         # Setup dependencies"
	@echo "  2. make dev           # Start both servers"
	@echo "  3. make test          # Run tests"
