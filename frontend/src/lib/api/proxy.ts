import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Backend API URL - using the correct port 8300
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8300';

interface ProxyOptions {
  requireAuth?: boolean;
  headers?: Record<string, string>;
}

/**
 * Generic proxy handler for forwarding requests to the backend API
 * This handles authentication, error handling, and response formatting
 */
export async function proxyRequest(
  request: NextRequest,
  backendPath: string,
  options: ProxyOptions = { requireAuth: true }
) {
  try {
    // Get the session if authentication is required
    let authHeader: Record<string, string> = {};
    
    if (options.requireAuth) {
      const session = await getServerSession(authOptions);
      
      if (!session?.accessToken) {
        return NextResponse.json(
          {
            success: false,
            message: 'Unauthorized',
            data: null,
          },
          { status: 401 }
        );
      }
      
      authHeader = { 'Authorization': `Bearer ${session.accessToken}` };
    }
    
    // Build the backend URL
    const url = new URL(backendPath, BACKEND_API_URL);
    
    // Copy query parameters from the original request
    const searchParams = request.nextUrl.searchParams;
    searchParams.forEach((value, key) => {
      url.searchParams.append(key, value);
    });
    
    // Prepare headers
    const headers = {
      'Content-Type': 'application/json',
      ...authHeader,
      ...options.headers,
    };
    
    // Prepare the request options
    const fetchOptions: RequestInit = {
      method: request.method,
      headers,
    };
    
    // Add body for non-GET requests
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      try {
        const body = await request.json();
        fetchOptions.body = JSON.stringify(body);
      } catch {
        // If body is not JSON, try to get it as text
        const body = await request.text();
        if (body) {
          fetchOptions.body = body;
        }
      }
    }
    
    // Make the request to the backend
    const response = await fetch(url.toString(), fetchOptions);
    
    // Get the response data
    let data;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }
    
    // Create the response with the same status code as the backend
    const nextResponse = NextResponse.json(data, { status: response.status });
    
    // Copy relevant headers from backend response
    const headersToForward = ['x-total-count', 'x-page', 'x-per-page'];
    headersToForward.forEach(header => {
      const value = response.headers.get(header);
      if (value) {
        nextResponse.headers.set(header, value);
      }
    });
    
    return nextResponse;
  } catch (error) {
    console.error('Proxy error:', error);
    
    // Handle network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unable to connect to backend service',
          data: null,
        },
        { status: 503 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        message: 'An error occurred while processing the request',
        data: null,
      },
      { status: 500 }
    );
  }
}

/**
 * Helper function to create route handlers
 */
export function createRouteHandlers(
  backendPath: string | ((request: NextRequest) => string),
  options: ProxyOptions = {}
) {
  const getPath = (request: NextRequest) => {
    if (typeof backendPath === 'function') {
      return backendPath(request);
    }
    return backendPath;
  };
  
  return {
    GET: (request: NextRequest) => proxyRequest(request, getPath(request), options),
    POST: (request: NextRequest) => proxyRequest(request, getPath(request), options),
    PUT: (request: NextRequest) => proxyRequest(request, getPath(request), options),
    DELETE: (request: NextRequest) => proxyRequest(request, getPath(request), options),
    PATCH: (request: NextRequest) => proxyRequest(request, getPath(request), options),
  };
}
