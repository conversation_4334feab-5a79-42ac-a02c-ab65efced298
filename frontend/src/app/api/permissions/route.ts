import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { get } from '@/lib/fetch/fetchClient';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8300';

/**
 * GET handler for fetching available permissions
 * This is a proxy to the backend API
 */
export async function GET(request: NextRequest) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Forward request to backend API
    const response = await get('/organizations/permissions', {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error fetching permissions:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to fetch permissions',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}
