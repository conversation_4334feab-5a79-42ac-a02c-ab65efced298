import NextAuth from "next-auth";
import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";

// Define the auth options
export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Make a request to the backend API for authentication
          const baseUrl = process.env.BACKEND_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8300';
          const response = await fetch(`${baseUrl}/api/auth/signin`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          });

          const data = await response.json();

          if (response.ok && data.success) {
            // Return the user object with tokens
            return {
              id: data.data.user.id,
              name: data.data.user.username,
              email: data.data.user.email,
              accessToken: data.data.access_token,
              refreshToken: data.data.refresh_token,
            };
          }

          // Log the error and return null to show the actual error
          console.warn("API authentication failed");
          return null;
        } catch (error) {
          console.error("Authentication error:", error);
          // Log the error and return null to show the actual error
          console.error("Authentication error details:", error);
          return null;
        }
      }
    })
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      // Only process Google sign-ins
      if (account?.provider === 'google') {
        try {
          // Send the Google auth data to our backend
          const baseUrl = process.env.BACKEND_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8300';
          const response = await fetch(`${baseUrl}/api/auth/google-auth`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: user.email,
              name: user.name,
              googleId: profile?.sub,
              picture: user.image,
            }),
          });

          const data = await response.json();

          if (response.ok && data.success) {
            // Store the tokens in the user object to be used in the jwt callback
            user.accessToken = data.data.access_token;
            user.refreshToken = data.data.refresh_token;
            user.id = data.data.user.id;
            return true;
          }

          console.warn("Google authentication failed on backend");
          return false;
        } catch (error) {
          console.error("Google authentication error:", error);
          return false;
        }
      }

      return true; // Allow other sign-in methods to proceed normally
    },
    async jwt({ token, user, account }) {
      // Initial sign in
      if (user) {
        console.log('JWT callback - user object:', user);
        token.id = user.id;
        token.accessToken = user.accessToken;
        token.refreshToken = user.refreshToken;

        // For Google auth, if we don't have tokens yet (should not happen with our flow)
        if (account?.provider === 'google' && !user.accessToken) {
          token.googleId = account.providerAccountId;
        }

        console.log('JWT callback - token after update:', token);
      }
      return token;
    },
    async session({ session, token }) {
      console.log('Session callback - token:', token);
      console.log('Session callback - session before update:', session);

      // Send properties to the client
      if (session.user) {
        session.user.id = token.id as string;
        // @ts-ignore - these properties are added to the session
        session.accessToken = token.accessToken;
        // @ts-ignore - these properties are added to the session
        session.refreshToken = token.refreshToken;
      }

      console.log('Session callback - session after update:', session);
      return session;
    }
  },
  pages: {
    signIn: '/auth/signin',
    signOut: '/auth/signout',
    error: '/auth/error',
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.NEXTAUTH_SECRET || "test-secret-please-change",
};

// Create the NextAuth handler
const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
